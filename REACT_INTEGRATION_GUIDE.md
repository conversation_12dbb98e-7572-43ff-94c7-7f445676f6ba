# React Frontend Integration Guide

## Quick Start

### 1. Setup React Project
```bash
npx create-react-app music-app
cd music-app
npm install socket.io-client axios
```

### 2. Environment Configuration
Create `.env` file:
```env
REACT_APP_API_BASE_URL=http://localhost:5000
REACT_APP_SOCKET_URL=http://localhost:5000
```

### 3. API Service Setup
```javascript
// src/services/api.js
import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000';

const api = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true, // Important for session cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Redirect to login
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
```

### 4. Authentication Hook
```javascript
// src/hooks/useAuth.js
import { useState, useEffect, createContext, useContext } from 'react';
import api from '../services/api';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    checkSession();
  }, []);

  const checkSession = async () => {
    try {
      const response = await api.get('/api/auth/session');
      setUser(response.data.user);
      
      // Check admin status
      try {
        await api.get('/api/admin/check');
        setIsAdmin(true);
      } catch {
        setIsAdmin(false);
      }
    } catch (error) {
      console.error('Session check failed:', error);
      setUser(null);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  const login = () => {
    window.location.href = `${process.env.REACT_APP_API_BASE_URL}/api/auth/sign-in/google`;
  };

  const logout = async () => {
    try {
      await api.post('/api/auth/sign-out');
      setUser(null);
      setIsAdmin(false);
      window.location.href = '/';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      loading, 
      isAdmin, 
      login, 
      logout, 
      checkSession 
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
```

### 5. Music Service
```javascript
// src/services/musicService.js
import api from './api';

export const musicService = {
  // Songs
  getFeaturedSongs: () => api.get('/api/songs/featured'),
  getMadeForYouSongs: () => api.get('/api/songs/made-for-you'),
  getTrendingSongs: () => api.get('/api/songs/trending'),
  getAllSongs: () => api.get('/api/songs'), // Admin only

  // Albums
  getAllAlbums: () => api.get('/api/albums'),
  getAlbumById: (id) => api.get(`/api/albums/${id}`),

  // Artists
  getAllArtists: () => api.get('/api/artists'),
  getArtistById: (id) => api.get(`/api/artists/${id}`),

  // Users
  getAllUsers: () => api.get('/api/users'),
  getMessages: (userId) => api.get(`/api/users/messages/${userId}`),

  // Admin
  createSong: (formData) => api.post('/api/admin/songs', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteSong: (id) => api.delete(`/api/admin/songs/${id}`),
  createAlbum: (formData) => api.post('/api/admin/albums', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteAlbum: (id) => api.delete(`/api/admin/albums/${id}`),
  createArtist: (formData) => api.post('/api/admin/artists', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteArtist: (id) => api.delete(`/api/admin/artists/${id}`),

  // Stats
  getStats: () => api.get('/api/stats'),
};
```

### 6. Socket Service
```javascript
// src/services/socketService.js
import io from 'socket.io-client';

class SocketService {
  constructor() {
    this.socket = null;
  }

  connect(userId) {
    this.socket = io(process.env.REACT_APP_SOCKET_URL, {
      withCredentials: true,
    });

    this.socket.on('connect', () => {
      console.log('Connected to server');
      this.socket.emit('joinRoom', userId);
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  sendMessage(receiverId, content) {
    if (this.socket) {
      this.socket.emit('sendMessage', { receiverId, content });
    }
  }

  onReceiveMessage(callback) {
    if (this.socket) {
      this.socket.on('receiveMessage', callback);
    }
  }
}

export const socketService = new SocketService();
```

### 7. Custom Hooks

#### Music Player Hook
```javascript
// src/hooks/usePlayer.js
import { useState, useRef, useEffect } from 'react';

export const usePlayer = () => {
  const [currentSong, setCurrentSong] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const audioRef = useRef(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleEnded = () => setIsPlaying(false);

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [currentSong]);

  const playSong = (song) => {
    setCurrentSong(song);
    setIsPlaying(true);
  };

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const seekTo = (time) => {
    const audio = audioRef.current;
    if (audio) {
      audio.currentTime = time;
      setCurrentTime(time);
    }
  };

  const changeVolume = (newVolume) => {
    const audio = audioRef.current;
    if (audio) {
      audio.volume = newVolume;
      setVolume(newVolume);
    }
  };

  return {
    currentSong,
    isPlaying,
    currentTime,
    duration,
    volume,
    audioRef,
    playSong,
    togglePlayPause,
    seekTo,
    changeVolume,
  };
};
```

#### Data Fetching Hook
```javascript
// src/hooks/useMusic.js
import { useState, useEffect } from 'react';
import { musicService } from '../services/musicService';

export const useMusic = () => {
  const [featuredSongs, setFeaturedSongs] = useState([]);
  const [madeForYou, setMadeForYou] = useState([]);
  const [trending, setTrending] = useState([]);
  const [albums, setAlbums] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchMusicData();
  }, []);

  const fetchMusicData = async () => {
    try {
      setLoading(true);
      const [featuredRes, madeForYouRes, trendingRes, albumsRes] = await Promise.all([
        musicService.getFeaturedSongs(),
        musicService.getMadeForYouSongs(),
        musicService.getTrendingSongs(),
        musicService.getAllAlbums(),
      ]);

      setFeaturedSongs(featuredRes.data);
      setMadeForYou(madeForYouRes.data);
      setTrending(trendingRes.data);
      setAlbums(albumsRes.data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return {
    featuredSongs,
    madeForYou,
    trending,
    albums,
    loading,
    error,
    refetch: fetchMusicData,
  };
};
```

### 8. Component Examples

#### Login Component
```javascript
// src/components/Login.js
import React from 'react';
import { useAuth } from '../hooks/useAuth';

const Login = () => {
  const { login, loading } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="login-container">
      <h1>Welcome to Music App</h1>
      <button onClick={login} className="login-button">
        Sign in with Google
      </button>
    </div>
  );
};

export default Login;
```

#### Music Player Component
```javascript
// src/components/MusicPlayer.js
import React from 'react';
import { usePlayer } from '../hooks/usePlayer';

const MusicPlayer = () => {
  const {
    currentSong,
    isPlaying,
    currentTime,
    duration,
    volume,
    audioRef,
    togglePlayPause,
    seekTo,
    changeVolume,
  } = usePlayer();

  if (!currentSong) return null;

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="music-player">
      <audio ref={audioRef} src={currentSong.audioUrl} />
      
      <div className="song-info">
        <img src={currentSong.imageUrl} alt={currentSong.title} />
        <div>
          <h4>{currentSong.title}</h4>
          <p>{currentSong.artist}</p>
        </div>
      </div>

      <div className="player-controls">
        <button onClick={togglePlayPause}>
          {isPlaying ? '⏸️' : '▶️'}
        </button>
        
        <div className="progress-bar">
          <span>{formatTime(currentTime)}</span>
          <input
            type="range"
            min="0"
            max={duration || 0}
            value={currentTime}
            onChange={(e) => seekTo(e.target.value)}
          />
          <span>{formatTime(duration)}</span>
        </div>

        <div className="volume-control">
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={volume}
            onChange={(e) => changeVolume(e.target.value)}
          />
        </div>
      </div>
    </div>
  );
};

export default MusicPlayer;
```

#### Song List Component
```javascript
// src/components/SongList.js
import React from 'react';
import { usePlayer } from '../hooks/usePlayer';

const SongList = ({ songs, title }) => {
  const { playSong } = usePlayer();

  return (
    <div className="song-list">
      <h2>{title}</h2>
      <div className="songs-grid">
        {songs.map((song) => (
          <div key={song._id} className="song-card" onClick={() => playSong(song)}>
            <img src={song.imageUrl} alt={song.title} />
            <h3>{song.title}</h3>
            <p>{song.artist}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SongList;
```

### 9. Main App Component
```javascript
// src/App.js
import React from 'react';
import { AuthProvider, useAuth } from './hooks/useAuth';
import { useMusic } from './hooks/useMusic';
import Login from './components/Login';
import MusicPlayer from './components/MusicPlayer';
import SongList from './components/SongList';
import './App.css';

const AppContent = () => {
  const { user, loading, logout } = useAuth();
  const { featuredSongs, madeForYou, trending, loading: musicLoading } = useMusic();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <Login />;
  }

  return (
    <div className="app">
      <header>
        <h1>Music App</h1>
        <div className="user-info">
          <img src={user.imageUrl} alt={user.fullName} />
          <span>{user.fullName}</span>
          <button onClick={logout}>Logout</button>
        </div>
      </header>

      <main>
        {musicLoading ? (
          <div>Loading music...</div>
        ) : (
          <>
            <SongList songs={featuredSongs} title="Featured Songs" />
            <SongList songs={madeForYou} title="Made For You" />
            <SongList songs={trending} title="Trending" />
          </>
        )}
      </main>

      <MusicPlayer />
    </div>
  );
};

const App = () => {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default App;
```

### 10. Basic CSS
```css
/* src/App.css */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.2);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.song-list {
  margin: 2rem;
}

.songs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.song-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.song-card:hover {
  transform: scale(1.05);
}

.song-card img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
}

.music-player {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.song-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.song-info img {
  width: 50px;
  height: 50px;
  border-radius: 4px;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.progress-bar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.progress-bar input {
  flex: 1;
}

.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 2rem;
}

.login-button {
  padding: 1rem 2rem;
  font-size: 1.2rem;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}
```

This React integration guide provides a complete foundation for building a music streaming frontend that works with your backend API.