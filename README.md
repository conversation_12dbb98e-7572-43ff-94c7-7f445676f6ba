# Mobile App Integration Guide for Backend Express API

Lets triiger main for gcp

## 1. Authentication

- The backend uses <PERSON> for authentication (`clerkMiddleware`).
- All protected routes require a valid Clerk token in the request headers.
- For user registration/login, POST to `/api/auth/callback` with:
  ```json
  {
    "id": "clerk_user_id",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "imageUrl": "https://..."
  }
  ```

## 2. API Endpoints

### Users
- `GET /api/users`  
  - Auth required.
  - Returns all users except the current user.
- `GET /api/users/messages/:userId`  
  - Auth required.
  - Returns chat messages between current user and `userId`.

### Songs
- `GET /api/songs`  
  - Auth + Admin required.
  - Returns all songs.
- `GET /api/songs/featured`  
  - Public.
  - Returns 6 random featured songs.
- `GET /api/songs/made-for-you`  
  - Public.
  - Returns 4 random songs.
- `GET /api/songs/trending`  
  - Public.
  - Returns 4 trending songs.

### Albums
- `GET /api/albums`  
  - Public.
  - Returns all albums.
- `GET /api/albums/:albumId`  
  - Public.
  - Returns album details and its songs.

### Admin (for admin users)
- `GET /api/admin/check`  
  - Auth + Admin required.
  - Returns `{ admin: true }` if user is admin.
- `POST /api/admin/songs`  
  - Auth + Admin required.
  - Upload new song (multipart/form-data: audioFile, imageFile, title, artist, albumId, duration).
- `DELETE /api/admin/songs/:id`  
  - Auth + Admin required.
  - Delete song by ID.
- `POST /api/admin/albums`  
  - Auth + Admin required.
  - Upload new album (multipart/form-data: imageFile, title, artist, releaseYear).
- `DELETE /api/admin/albums/:id`  
  - Auth + Admin required.
  - Delete album by ID.

### Stats
- `GET /api/stats`  
  - Auth + Admin required.
  - Returns total counts for songs, albums, users, and artists.

## 3. Request Headers

- For protected routes, include Clerk token:
  ```
  Authorization: Bearer <clerk_token>
  ```

## 4. Data Models

### User
```json
{
  "fullName": "John Doe",
  "imageUrl": "https://...",
  "clerkId": "clerk_user_id"
}
```

### Song
```json
{
  "title": "Song Title",
  "artist": "Artist Name",
  "imageUrl": "https://...",
  "audioUrl": "https://...",
  "duration": 180,
  "albumId": "album_object_id"
}
```

### Album
```json
{
  "title": "Album Title",
  "artist": "Artist Name",
  "imageUrl": "https://...",
  "releaseYear": 2024,
  "songs": ["song_object_id", ...]
}
```

## 5. Integration Tips

- Use HTTPS for production.
- Handle 401/403 errors for protected/admin routes.
- For file uploads (songs/albums), use multipart/form-data.
- Use the provided endpoints for music, albums, user info, and chat features.
- For chat, use `/api/users/messages/:userId`.

---

For questions or more details on any endpoint, see the backend source code or ask the maintainer.
