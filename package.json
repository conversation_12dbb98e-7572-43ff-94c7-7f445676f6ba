{"name": "backend", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc --noEmitOnError false", "dev": "nodemon --exec tsx src/index.ts", "start": "node dist/index.ts", "seed:songs": "tsx src/seeds/songs.ts", "seed:albums": "tsx src/seeds/albums.ts", "fix-playlists": "tsx src/scripts/fix-orphaned-playlists.ts", "investigate-db": "tsx src/scripts/investigate-collections.ts", "cleanup-db": "tsx src/scripts/cleanup-collections.ts", "test-users": "tsx src/scripts/test-user-functionality.ts"}, "keywords": [], "type": "module", "author": "", "license": "ISC", "dependencies": {"@clerk/express": "^1.3.4", "better-auth": "^1.2.12", "cloudinary": "^2.5.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "express-fileupload": "^1.5.1", "mongodb": "^6.17.0", "mongoose": "^8.8.0", "node-cron": "^3.0.3", "socket.io": "^4.8.1"}, "devDependencies": {"@better-auth/cli": "^1.2.12", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.5.1", "@types/node": "^22.0.0", "@types/node-cron": "^3.0.11", "nodemon": "^3.1.7", "tsx": "^4.20.3", "typescript": "^5.4.5"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b"}