{"designSystem": {"name": "Music Streaming Platform UI", "version": "1.0.0", "description": "A modern music streaming interface design system with card-based layouts and vibrant visual elements", "colorPalette": {"primary": {"background": "#f5f5f7", "surfaceLight": "#ffffff", "surfaceMedium": "#e8e8ea", "textPrimary": "#1d1d1f", "textSecondary": "#86868b", "textMuted": "#a1a1a6"}, "accent": {"primary": "#D9AD39", "blue": "#007aff", "cyan": "#32d2d2", "red": "#ff3b30", "purple": "#5856d6"}, "gradients": {"heroCard": "linear-gradient(135deg, #D9AD39 0%, #B8941F 100%)", "darkCard": "linear-gradient(135deg, #1a1a2e 0%, #16213e 100%)", "lightCard": "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)"}}, "typography": {"fontFamily": {"primary": "SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif", "secondary": "SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif"}, "hierarchy": {"heroTitle": {"fontSize": "48px", "fontWeight": "700", "lineHeight": "1.1", "letterSpacing": "-0.025em"}, "cardTitle": {"fontSize": "24px", "fontWeight": "600", "lineHeight": "1.3"}, "subtitle": {"fontSize": "16px", "fontWeight": "400", "lineHeight": "1.4", "opacity": "0.8"}, "metadata": {"fontSize": "14px", "fontWeight": "500", "lineHeight": "1.4"}, "caption": {"fontSize": "12px", "fontWeight": "400", "lineHeight": "1.3"}}}, "layout": {"structure": {"type": "sidebar-main", "sidebar": {"width": "240px", "position": "fixed", "background": "surfaceLight"}, "main": {"marginLeft": "240px", "padding": "24px", "maxWidth": "1200px"}}, "spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "xxl": "48px"}, "grid": {"columns": 12, "gap": "20px", "breakpoints": {"mobile": "768px", "tablet": "1024px", "desktop": "1200px"}}}, "components": {"navigation": {"sidebar": {"structure": "vertical-menu", "sections": [{"type": "brand", "position": "top", "fontSize": "18px", "fontWeight": "600"}, {"type": "primary-nav", "items": ["Browse Music", "Library"], "style": "section-headers"}, {"type": "menu-items", "style": "icon-text-pairs", "spacing": "sm", "hoverEffect": "subtle-background"}]}, "topBar": {"structure": "breadcrumb-search-user", "elements": ["breadcrumb-navigation", "search-bar", "user-profile"]}}, "cards": {"heroCard": {"type": "featured-content", "dimensions": "large-landscape", "aspectRatio": "16:9", "background": "gradient-with-image", "contentPosition": "overlay-left", "borderRadius": "16px", "shadow": "elevated", "elements": [{"type": "badge", "position": "top-left", "style": "small-caps-text"}, {"type": "title", "style": "large-bold-white"}, {"type": "description", "style": "multi-line-subtle"}, {"type": "metadata", "style": "stats-row", "items": ["likes", "song-count", "duration"]}]}, "contentCard": {"type": "media-tile", "dimensions": "square-medium", "aspectRatio": "1:1", "borderRadius": "12px", "shadow": "soft", "hoverEffect": "lift-scale", "elements": [{"type": "image", "position": "full-cover"}, {"type": "title", "position": "below", "style": "medium-weight"}, {"type": "subtitle", "position": "below", "style": "muted-text"}]}, "listCard": {"type": "horizontal-item", "structure": "image-content-actions", "spacing": "md", "elements": [{"type": "thumbnail", "size": "small-square", "borderRadius": "8px"}, {"type": "content", "layout": "vertical-stack"}, {"type": "actions", "style": "icon-buttons"}]}}, "interactions": {"hover": {"cards": "subtle-lift-shadow", "buttons": "background-opacity-change", "navigation": "background-highlight"}, "active": {"navigation": "selected-state-indicator", "cards": "pressed-scale-down"}, "focus": {"style": "outline-ring", "color": "accent-blue"}}, "mediaPlayer": {"position": "bottom-fixed", "structure": "track-info-controls-volume", "background": "surface-light", "height": "80px", "elements": [{"type": "track-info", "layout": "image-title-artist"}, {"type": "controls", "layout": "previous-play-next", "style": "circular-buttons"}, {"type": "progress", "style": "timeline-scrubber"}, {"type": "volume", "style": "icon-slider"}]}}, "visualElements": {"images": {"treatment": "high-contrast-vibrant", "overlays": "subtle-gradients", "placeholders": "geometric-patterns"}, "shadows": {"soft": "0 2px 8px rgba(0,0,0,0.1)", "elevated": "0 8px 24px rgba(0,0,0,0.15)", "dramatic": "0 16px 48px rgba(0,0,0,0.2)"}, "borders": {"radius": {"small": "8px", "medium": "12px", "large": "16px", "round": "50%"}}, "animations": {"duration": {"fast": "150ms", "medium": "300ms", "slow": "500ms"}, "easing": "cubic-bezier(0.4, 0, 0.2, 1)"}}, "responsivePatterns": {"mobile": {"navigation": "bottom-tab-bar", "cards": "single-column-stack", "hero": "reduced-height"}, "tablet": {"navigation": "collapsible-sidebar", "cards": "two-column-grid", "hero": "maintain-aspect-ratio"}, "desktop": {"navigation": "persistent-sidebar", "cards": "multi-column-grid", "hero": "full-featured"}}, "contentPatterns": {"musicLibrary": {"layout": "grid-with-hero", "heroContent": "featured-playlist", "gridContent": "album-artist-tiles", "sidebar": "navigation-library"}, "discovery": {"layout": "feed-style", "sections": ["personalized-recommendations", "trending-content", "genre-categories"]}}, "designPrinciples": {"hierarchy": "Clear visual hierarchy with prominent hero content and organized secondary content", "contrast": "High contrast between content and background for readability", "consistency": "Consistent card-based layouts and spacing throughout", "accessibility": "Proper color contrast and focus states for all interactive elements", "modernism": "Clean, minimal design with subtle shadows and rounded corners", "imagery": "Rich, vibrant imagery as primary visual element with text overlays"}}}