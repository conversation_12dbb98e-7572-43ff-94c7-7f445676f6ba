import { betterAuth } from "better-auth";
import { mongodbAdapter } from "better-auth/adapters/mongodb";
import { jwt } from "better-auth/plugins/jwt";
import { getNativeMongoDb } from "./lib/db.js";
import { User } from "./models/user.model.js";
// Create Better Auth configuration function using native MongoDB driver
export const createAuth = () => {
    const db = getNativeMongoDb();
    // Debug log the environment variables
    console.log("Google Client ID exists:", !!process.env.GOOGLE_CLIENT_ID);
    console.log("Google Client Secret exists:", !!process.env.GOOGLE_CLIENT_SECRET);
    return betterAuth({
        // Required environment variables
        secret: process.env.BETTER_AUTH_SECRET || "fallback-secret-key",
        baseURL: process.env.BACKEND_BASE_URL || process.env.BETTER_AUTH_URL || "http://localhost:5000",
        basePath: "/api/auth",
        // Trusted origins for CORS
        trustedOrigins: [
            process.env.FRONTEND_BASE_URL || "http://localhost:3000",
            process.env.BACKEND_BASE_URL || "http://localhost:5000"
        ],
        // MongoDB adapter - use native driver
        database: mongodbAdapter(db, {
            debugLogs: process.env.NODE_ENV === "development",
        }),
        // OAuth providers (replacing Clerk's Google OAuth)
        socialProviders: {
            google: {
                prompt: "select_account",
                clientId: process.env.GOOGLE_CLIENT_ID,
                clientSecret: process.env.GOOGLE_CLIENT_SECRET,
            },
        },
        // JWT plugin for session management
        plugins: [
            jwt(),
        ],
        // Callbacks
        callbacks: {
            onSignIn: async ({ user, account }) => {
                // Handle sign in logic and sync with custom User model
                console.log("User signed in:", user.email);
                try {
                    // Check if user exists in custom User collection
                    const existingUser = await User.findById(user.id);
                    if (!existingUser) {
                        // Create user in custom User collection if it doesn't exist
                        await User.create({
                            _id: user.id,
                            name: user.name || user.email?.split('@')[0] || 'Unknown User',
                            image: user.image || '/default-avatar.png',
                            email: user.email,
                            emailVerified: user.emailVerified || false,
                        });
                        console.log("Created user in custom User collection:", user.email);
                    }
                    else {
                        // Update user info in case it changed
                        await User.findByIdAndUpdate(user.id, {
                            name: user.name || user.email?.split('@')[0] || existingUser.name,
                            image: user.image || existingUser.image,
                            email: user.email || existingUser.email,
                            emailVerified: user.emailVerified || existingUser.emailVerified,
                        });
                        console.log("Updated user in custom User collection:", user.email);
                    }
                }
                catch (error) {
                    console.error("Error syncing user with custom User model:", error);
                }
                return true;
            },
            onUserCreate: async ({ user }) => {
                // Handle user creation logic and sync with custom User model
                console.log("User created:", user.email);
                try {
                    // Create user in custom User collection
                    await User.create({
                        _id: user.id,
                        name: user.name || user.email?.split('@')[0] || 'Unknown User',
                        image: user.image || '/default-avatar.png',
                        email: user.email,
                        emailVerified: user.emailVerified || false,
                    });
                    console.log("Created user in custom User collection:", user.email);
                }
                catch (error) {
                    console.error("Error creating user in custom User model:", error);
                    // Don't fail the auth process if custom user creation fails
                }
                return true;
            },
        },
        // Default redirect URL after successful authentication
        defaultRedirect: process.env.FRONTEND_BASE_URL || "http://localhost:3000",
        onAPIError: {
            errorURL: `${process.env.FRONTEND_BASE_URL || "http://localhost:3000"}/auth/error`
        }
    });
};
// Initialize auth after database connection
let auth = null;
export const initializeAuth = () => {
    if (!auth) {
        auth = createAuth();
    }
    return auth;
};
export const getAuth = () => {
    if (!auth) {
        throw new Error("Auth not initialized. Call initializeAuth() first.");
    }
    return auth;
};
//# sourceMappingURL=auth.js.map