import { Request } from "express";
export interface BetterAuthUser {
    id: string;
    email: string;
    name?: string;
    image?: string | null;
}
export interface BetterAuthSession {
    session: {
        id: string;
        token: string;
        userId: string;
        expiresAt: Date;
        createdAt: Date;
        updatedAt: Date;
        ipAddress?: string | null | undefined | undefined;
        userAgent?: string | null | undefined | undefined;
    };
    user: {
        id: string;
        email: string;
        name?: string;
        image?: string | null;
    };
}
export interface AuthenticatedRequest<P = any, ResBody = any, ReqBody = any> extends Request<P, ResBody, ReqBody> {
    auth?: BetterAuthSession;
}
//# sourceMappingURL=index.d.ts.map