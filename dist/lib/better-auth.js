import { betterAuth } from "better-auth";
import { mongodbAdapter } from "better-auth/adapters/mongodb";
import { jwt } from "better-auth/plugins/jwt";
import mongoose from "mongoose";
// Better Auth configuration
export const auth = betterAuth({
    // Required environment variables
    secret: process.env.BETTER_AUTH_SECRET,
    baseURL: process.env.BETTER_AUTH_URL || "http://localhost:5000",
    // MongoDB adapter using native MongoDB connection
    database: mongodbAdapter(mongoose.connection.db, {
        debugLogs: process.env.NODE_ENV === "development",
    }),
    // OAuth providers (replacing Clerk's Google OAuth)
    socialProviders: {
        google: {
            prompt: "select_account",
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        },
    },
    // JWT plugin for session management
    plugins: [
        jwt(),
    ],
    // Callbacks
    callbacks: {
        onSignIn: async ({ user, account }) => {
            // Handle sign in logic
            console.log("User signed in:", user.email);
            return true;
        },
        onUserCreate: async ({ user }) => {
            // Handle user creation logic
            console.log("User created:", user.email);
            return true;
        },
    },
});
// Export the auth handler for Express
export const authHandler = auth.handler;
//# sourceMappingURL=better-auth.js.map