{"version": 3, "file": "socket.js", "sourceRoot": "", "sources": ["../../src/lib/socket.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAEnC,OAAO,EAAE,OAAO,EAAE,MAAM,4BAA4B,CAAC;AAarD,IAAI,EAAU,CAAC;AAEf,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,MAAkB,EAAE,EAAE;IACrD,EAAE,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE;QACtB,IAAI,EAAE;YACJ,MAAM,EAAE;gBACN,uBAAuB;gBAEvB,wCAAwC;aACzC;YACD,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;YACxB,WAAW,EAAE,IAAI;SAClB;KACF,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,IAAI,GAAG,EAAkB,CAAC,CAAC,sBAAsB;IACrE,MAAM,cAAc,GAAG,IAAI,GAAG,EAAkB,CAAC,CAAC,qBAAqB;IAEvE,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;QAC7B,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QAE1C,MAAM,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,MAAc,EAAE,EAAE;YAC7C,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;YACnC,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAEnC,mEAAmE;YACnE,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAElC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAE5D,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAgB,EAAE,EAAE;YAClE,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YAClD,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACrC,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,EAAE,IAAiB,EAAE,EAAE;YACpD,IAAI,CAAC;gBACH,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;gBAE/C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC;oBACnC,QAAQ;oBACR,UAAU;oBACV,OAAO;iBACR,CAAC,CAAC;gBAEH,kDAAkD;gBAClD,MAAM,gBAAgB,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACrD,IAAI,gBAAgB,EAAE,CAAC;oBACrB,EAAE,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;gBAC3D,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YACvC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;YACzD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,MAAc,EAAE,EAAE;YACxC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,oBAAoB,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,IAAI,EAAE,EAAE;YACjC,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;YACtC,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;YAC3B,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;YAC7C,IAAI,kBAAsC,CAAC;YAC3C,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;gBACvD,yBAAyB;gBACzB,IAAI,QAAQ,KAAK,MAAM,CAAC,EAAE,EAAE,CAAC;oBAC3B,kBAAkB,GAAG,MAAM,CAAC;oBAC5B,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC3B,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC9B,MAAM;gBACR,CAAC;YACH,CAAC;YACD,IAAI,kBAAkB,EAAE,CAAC;gBACvB,EAAE,CAAC,IAAI,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAAG,GAAG,EAAE;IACxB,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAChD,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC"}