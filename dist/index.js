import express from "express";
import dotenv from "dotenv";
import fileUpload from "express-fileupload";
import path from "path";
import cors from "cors";
import fs from "fs";
import { createServer } from "http";
import cron from "node-cron";
import { toN<PERSON><PERSON><PERSON><PERSON> } from "better-auth/node";
import { initializeSocket } from "./lib/socket.js";
import { connectDB } from "./lib/db.js";
import { initializeAuth, getAuth } from "./auth.js";
import userRoutes from "./routes/user.route.js";
import adminRoutes from "./routes/admin.route.js";
import songRoutes from "./routes/song.route.js";
import albumRoutes from "./routes/album.route.js";
import artistRoutes from "./routes/artist.route.js";
import statRoutes from "./routes/stat.route.js";
import engagementRoutes from "./routes/engagement.route.js";
import playlistRoutes from "./routes/playlist.route.js";
dotenv.config();
const __dirname = path.resolve();
const app = express();
const PORT = process.env.PORT || 5000;
const httpServer = createServer(app);
initializeSocket(httpServer);
app.use(cors({
    origin: ["http://localhost:3000", "https://app.strictlylesothomusic.co.ls"],
    credentials: true,
}));
// Initialize Better Auth after database connection
let auth = null;
// Better Auth routes - mount using toNodeHandler as per documentation
app.all("/api/auth/*", (req, res, next) => {
    if (!auth) {
        try {
            auth = getAuth();
        }
        catch (error) {
            return res.status(503).json({
                error: "Authentication service not ready. Please try again in a moment.",
            });
        }
    }
    const handler = toNodeHandler(auth);
    return handler(req, res);
});
// Mount express json middleware AFTER Better Auth handler (as per documentation)
app.use(express.json());
// Root route - redirect to frontend (handles OAuth redirects)
app.get("/", (req, res) => {
    const frontendUrl = process.env.FRONTEND_BASE_URL || "http://localhost:3000";
    res.redirect(frontendUrl);
});
app.use(fileUpload({
    useTempFiles: true,
    tempFileDir: path.join(__dirname, "tmp"),
    createParentPath: true,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB  max file size
    },
}));
// cron jobs
const tempDir = path.join(process.cwd(), "tmp");
cron.schedule("0 * * * *", () => {
    if (fs.existsSync(tempDir)) {
        fs.readdir(tempDir, (err, files) => {
            if (err) {
                console.log("error", err);
                return;
            }
            for (const file of files) {
                fs.unlink(path.join(tempDir, file), (err) => { });
            }
        });
    }
});
app.use("/api/users", userRoutes);
app.use("/api/admin", adminRoutes);
app.use("/api/songs", songRoutes);
app.use("/api/albums", albumRoutes);
app.use("/api/artists", artistRoutes);
app.use("/api/stats", statRoutes);
app.use("/api/engagement", engagementRoutes);
app.use("/api/playlists", playlistRoutes);
if (process.env.NODE_ENV === "production") {
    app.use(express.static(path.join(__dirname, "../frontend/dist")));
    app.get("*", (req, res) => {
        res.sendFile(path.resolve(__dirname, "../frontend", "dist", "index.html"));
    });
}
// error handler
app.use((err, req, res, next) => {
    res
        .status(500)
        .json({
        message: process.env.NODE_ENV === "production"
            ? "Internal server error"
            : err.message,
    });
});
httpServer.listen(PORT, async () => {
    console.log("Server is running on port " + PORT);
    await connectDB();
    // Initialize Better Auth after database connection is ready
    try {
        initializeAuth();
        console.log("Better Auth initialized successfully");
    }
    catch (error) {
        console.error("Failed to initialize Better Auth:", error);
    }
});
//# sourceMappingURL=index.js.map