import { Router } from "express";
import { checkAdmin, createAlbum, createSong, deleteAlbum, deleteSong, createArtist, deleteArtist, updateSong, updateAlbum, updateArtist, fixMissingArtistIds, } from "../controller/admin.controller.js";
import { protectRoute, requireAdmin } from "../middleware/auth.middleware.js";
const router = Router();
router.use(protectRoute, requireAdmin);
router.get("/check", checkAdmin);
router.post("/fix-artist-ids", fixMissingArtistIds);
router.post("/songs", createSong);
router.put("/songs/:id", updateSong);
router.delete("/songs/:id", deleteSong);
router.post("/albums", createAlbum);
router.put("/albums/:id", updateAlbum);
router.delete("/albums/:id", deleteAlbum);
router.post("/artists", createArtist);
router.put("/artists/:id", updateArtist);
router.delete("/artists/:id", deleteArtist);
export default router;
//# sourceMappingURL=admin.route.js.map