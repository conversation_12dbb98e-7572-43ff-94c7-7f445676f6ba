import { Router } from "express";
import { getAllSongs, getFeaturedSongs, getMadeForYouSongs, getTrendingSongs, getSong } from "../controller/song.controller.js";
import { protectRoute } from "../middleware/auth.middleware.js";
const router = Router();
router.get("/", protectRoute, getAllSongs);
router.get("/featured", getFeaturedSongs);
router.get("/made-for-you", getMadeForYouSongs);
router.get("/trending", getTrendingSongs);
router.get("/:songId", protectRoute, getSong); // Assuming this is for fetching a specific song by ID
export default router;
//# sourceMappingURL=song.route.js.map