import { Router } from "express";
import { toggleSongLike, toggleArtistLike, toggleArtistFollow, trackSongPlay, getUserLikedSongs, getUserFollowedArtists, getUserPlayHistory, checkUserEngagement, getPopularArtists } from "../controller/engagement.controller.js";
import { protectRoute } from "../middleware/auth.middleware.js";
const router = Router();
// Like/Unlike endpoints
router.post("/songs/:songId/like", protectRoute, toggleSongLike);
router.post("/artists/:artistId/like", protectRoute, toggleArtistLike);
// Follow/Unfollow endpoints
router.post("/artists/:artistId/follow", protectRoute, toggleArtistFollow);
// Play tracking
router.post("/plays/track", protectRoute, trackSongPlay);
// User engagement data
router.get("/user/liked-songs", protectRoute, getUserLikedSongs);
router.get("/user/followed-artists", protectRoute, getUserFollowedArtists);
router.get("/user/play-history", protectRoute, getUserPlayHistory);
// Bulk check engagement status
router.post("/user/check", protectRoute, checkUserEngagement);
// Get popular artists (public endpoint)
router.get("/popular-artists", getPopularArtists);
export default router;
//# sourceMappingURL=engagement.route.js.map