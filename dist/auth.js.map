{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../src/auth.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AACzC,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAE9D,OAAO,EAAE,GAAG,EAAE,MAAM,yBAAyB,CAAC;AAC9C,OAAO,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,MAAM,wBAAwB,CAAC;AAE9C,wEAAwE;AACxE,MAAM,CAAC,MAAM,UAAU,GAAG,GAAG,EAAE;IAC7B,MAAM,EAAE,GAAG,gBAAgB,EAAE,CAAC;IAE9B,sCAAsC;IACtC,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACxE,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAEhF,OAAO,UAAU,CAAC;QAChB,iCAAiC;QACjC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,qBAAqB;QAC/D,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,uBAAuB;QAC/F,QAAQ,EAAE,WAAW;QAErB,2BAA2B;QAC3B,cAAc,EAAE;YACd,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,uBAAuB;YACxD,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,uBAAuB;SACxD;QAED,sCAAsC;QACtC,QAAQ,EAAE,cAAc,CAAC,EAAE,EAAE;YAC3B,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;SAClD,CAAC;QAEF,mDAAmD;QACnD,eAAe,EAAE;YACf,MAAM,EAAE;gBACN,MAAM,EAAE,gBAAgB;gBACxB,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAiB;gBACvC,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAqB;aAChD;SACF;QAED,oCAAoC;QACpC,OAAO,EAAE;YACP,GAAG,EAAE;SACN;QAED,YAAY;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAA+B,EAAE,EAAE;gBACjE,uDAAuD;gBACvD,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE3C,IAAI,CAAC;oBACH,iDAAiD;oBACjD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAElD,IAAI,CAAC,YAAY,EAAE,CAAC;wBAClB,4DAA4D;wBAC5D,MAAM,IAAI,CAAC,MAAM,CAAC;4BAChB,GAAG,EAAE,IAAI,CAAC,EAAE;4BACZ,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,cAAc;4BAC9D,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,qBAAqB;4BAC1C,KAAK,EAAE,IAAI,CAAC,KAAK;4BACjB,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,KAAK;yBAC3C,CAAC,CAAC;wBACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;oBACrE,CAAC;yBAAM,CAAC;wBACN,sCAAsC;wBACtC,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE;4BACpC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,IAAI;4BACjE,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK;4BACvC,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK;4BACvC,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,YAAY,CAAC,aAAa;yBAChE,CAAC,CAAC;wBACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;gBACrE,CAAC;gBAED,OAAO,IAAI,CAAC;YACd,CAAC;YAED,YAAY,EAAE,KAAK,EAAE,EAAE,IAAI,EAAiB,EAAE,EAAE;gBAC9C,6DAA6D;gBAC7D,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEzC,IAAI,CAAC;oBACH,wCAAwC;oBACxC,MAAM,IAAI,CAAC,MAAM,CAAC;wBAChB,GAAG,EAAE,IAAI,CAAC,EAAE;wBACZ,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,cAAc;wBAC9D,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,qBAAqB;wBAC1C,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,KAAK;qBAC3C,CAAC,CAAC;oBACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;oBAClE,4DAA4D;gBAC9D,CAAC;gBAED,OAAO,IAAI,CAAC;YACd,CAAC;SACF;QAED,uDAAuD;QACvD,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,uBAAuB;QACzE,UAAU,EAAE;YACV,QAAQ,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,uBAAuB,aAAa;SACnF;KACF,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,4CAA4C;AAC5C,IAAI,IAAI,GAAyC,IAAI,CAAC;AAEtD,MAAM,CAAC,MAAM,cAAc,GAAG,GAAG,EAAE;IACjC,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,IAAI,GAAG,UAAU,EAAE,CAAC;IACtB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAG,GAAG,EAAE;IAC1B,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IACxE,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC"}