import { Request, Response, NextFunction } from "express";
export declare const getAllSongs: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getFeaturedSongs: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getMadeForYouSongs: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getSong: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const getTrendingSongs: (req: Request, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=song.controller.d.ts.map