import { Response, NextFunction } from "express";
import type { AuthenticatedRequest } from "../types/index.js";
interface CreateSongBody {
    title: string;
    artist: string;
    albumId?: string;
    artistId?: string;
    featuredArtists?: string;
    duration: number;
    releaseDate: string;
    composer?: string;
    producer?: string;
    source?: string;
    credits?: string;
}
interface CreateAlbumBody {
    title: string;
    artist: string;
    artistId?: string;
    releaseYear: number;
    genre: string;
}
interface CreateArtistBody {
    name: string;
    bgColor?: string;
    about?: string;
    monthlyListeners?: string;
    genres?: string;
    socialLinks?: string;
}
export declare const createSong: (req: AuthenticatedRequest<{}, any, CreateSongBody>, res: Response, next: NextFunction) => Promise<void>;
export declare const deleteSong: (req: AuthenticatedRequest<{
    id: string;
}>, res: Response, next: NextFunction) => Promise<void>;
export declare const createAlbum: (req: AuthenticatedRequest<{}, any, CreateAlbumBody>, res: Response, next: NextFunction) => Promise<void>;
export declare const deleteAlbum: (req: AuthenticatedRequest<{
    id: string;
}>, res: Response, next: NextFunction) => Promise<void>;
export declare const createArtist: (req: AuthenticatedRequest<{}, any, CreateArtistBody>, res: Response, next: NextFunction) => Promise<void>;
export declare const deleteArtist: (req: AuthenticatedRequest<{
    id: string;
}>, res: Response, next: NextFunction) => Promise<void>;
export declare const updateSong: (req: AuthenticatedRequest<{
    id: string;
}, any, CreateSongBody>, res: Response, next: NextFunction) => Promise<void>;
export declare const updateAlbum: (req: AuthenticatedRequest<{
    id: string;
}, any, CreateAlbumBody>, res: Response, next: NextFunction) => Promise<void>;
export declare const updateArtist: (req: AuthenticatedRequest<{
    id: string;
}, any, CreateArtistBody>, res: Response, next: NextFunction) => Promise<void>;
export declare const checkAdmin: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const fixMissingArtistIds: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=admin.controller.d.ts.map