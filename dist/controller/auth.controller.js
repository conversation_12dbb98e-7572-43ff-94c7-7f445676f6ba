// Better Auth handles user creation automatically
// This controller can be used for additional auth-related operations if needed
export const getCurrentUser = async (req, res, next) => {
    try {
        // Better Auth handles session management
        // This endpoint can be used to get additional user data if needed
        res.status(200).json({ message: "Auth is handled by Better Auth" });
    }
    catch (error) {
        console.log("Error in auth controller", error);
        next(error);
    }
};
//# sourceMappingURL=auth.controller.js.map