import { Response, NextFunction } from "express";
import type { AuthenticatedRequest } from "../types/index.js";
export declare const toggleSongLike: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const toggleArtistLike: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const toggleArtistFollow: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const trackSongPlay: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getUserLikedSongs: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getUserFollowedArtists: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getUserPlayHistory: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const checkUserEngagement: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getPopularArtists: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=engagement.controller.d.ts.map