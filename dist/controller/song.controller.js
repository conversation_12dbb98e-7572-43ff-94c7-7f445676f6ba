import { Song } from "../models/song.model.js";
export const getAllSongs = async (req, res, next) => {
    try {
        // -1 = Descending => newest -> oldest
        // 1 = Ascending => oldest -> newest
        const songs = await Song.find().sort({ createdAt: -1 });
        res.json(songs);
    }
    catch (error) {
        next(error);
    }
};
export const getFeaturedSongs = async (req, res, next) => {
    try {
        // fetch 6 random songs using mongodb's aggregation pipeline
        const songs = await Song.aggregate([
            {
                $sample: { size: 6 },
            },
        ]);
        res.json(songs);
    }
    catch (error) {
        next(error);
    }
};
export const getMadeForYouSongs = async (req, res, next) => {
    try {
        const songs = await Song.aggregate([
            {
                $sample: { size: 4 },
            },
        ]);
        res.json(songs);
    }
    catch (error) {
        next(error);
    }
};
export const getSong = async (req, res, next) => {
    try {
        const { songId } = req.params;
        const song = await Song.findById(songId);
        if (!song) {
            res.status(404).json({ message: "Song not found" });
            return;
        }
        res.json(song);
    }
    catch (error) {
        next(error);
    }
};
export const getTrendingSongs = async (req, res, next) => {
    try {
        const songs = await Song.aggregate([
            {
                $sample: { size: 4 },
            },
        ]);
        res.json(songs);
    }
    catch (error) {
        next(error);
    }
};
//# sourceMappingURL=song.controller.js.map