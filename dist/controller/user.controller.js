import { User } from "../models/user.model.js";
import { Message } from "../models/message.model.js";
export const getAllUsers = async (req, res, next) => {
    try {
        const currentUserId = req.auth?.user.id;
        if (!currentUserId) {
            res.status(401).json({ message: "Unauthorized" });
            return;
        }
        const users = await User.find({ _id: { $ne: currentUserId } });
        res.status(200).json(users);
    }
    catch (error) {
        next(error);
    }
};
export const getMessages = async (req, res, next) => {
    try {
        const myId = req.auth?.user.id;
        if (!myId) {
            res.status(401).json({ message: "Unauthorized" });
            return;
        }
        const { userId } = req.params;
        const messages = await Message.find({
            $or: [
                { senderId: userId, receiverId: myId },
                { senderId: myId, receiverId: userId },
            ],
        }).sort({ createdAt: 1 });
        res.status(200).json(messages);
    }
    catch (error) {
        next(error);
    }
};
//# sourceMappingURL=user.controller.js.map