import { Response, NextFunction } from "express";
import type { AuthenticatedRequest } from "../types/index.js";
export declare const getAllUsers: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getMessages: (req: AuthenticatedRequest<{
    userId: string;
}>, res: Response, next: NextFunction) => Promise<void>;
//# sourceMappingURL=user.controller.d.ts.map