import mongoose, { Document } from "mongoose";
export interface IUserFollow extends Document {
    userId: mongoose.Types.ObjectId;
    artistId: mongoose.Types.ObjectId;
}
export declare const UserFollow: mongoose.Model<IUserFollow, {}, {}, {}, mongoose.Document<unknown, {}, IUserFollow, {}> & IUserFollow & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=userFollow.model.d.ts.map