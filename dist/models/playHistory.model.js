import mongoose, { Schema } from "mongoose";
const playHistorySchema = new Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    songId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Song",
        required: true,
    },
    artistId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Artist",
        required: true,
    },
    playedAt: {
        type: Date,
        required: true,
        default: Date.now,
    },
    duration: {
        type: Number,
        required: true,
        min: 0,
    },
    completed: {
        type: Boolean,
        required: true,
        default: false,
    },
    sessionId: {
        type: String,
        required: false,
    },
}, { timestamps: true });
// Index for duplicate prevention (userId + songId + playedAt within 5 minutes)
playHistorySchema.index({ userId: 1, songId: 1, playedAt: 1 });
// Index for song play count aggregation
playHistorySchema.index({ songId: 1 });
// Index for artist play count aggregation
playHistorySchema.index({ artistId: 1 });
// Index for time-based queries
playHistorySchema.index({ playedAt: 1 });
// Index for user's play history
playHistorySchema.index({ userId: 1, playedAt: -1 });
export const PlayHistory = mongoose.model("PlayHistory", playHistorySchema);
//# sourceMappingURL=playHistory.model.js.map