import mongoose, { Schema } from "mongoose";
const userFollowSchema = new Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    artistId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Artist",
        required: true,
    },
}, { timestamps: true });
// Compound index to ensure a user can only follow an artist once
userFollowSchema.index({ userId: 1, artistId: 1 }, { unique: true });
// Index for follower count queries
userFollowSchema.index({ artistId: 1 });
export const UserFollow = mongoose.model("UserFollow", userFollowSchema);
//# sourceMappingURL=userFollow.model.js.map