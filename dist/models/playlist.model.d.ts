import mongoose, { Document } from "mongoose";
export interface IPlaylist extends Document {
    name: string;
    description?: string;
    imageUrl?: string;
    userId: mongoose.Types.ObjectId;
    songs: mongoose.Types.ObjectId[];
    isPublic: boolean;
    collaborators?: mongoose.Types.ObjectId[];
    createdAt: Date;
    updatedAt: Date;
}
export declare const Playlist: mongoose.Model<IPlaylist, {}, {}, {}, mongoose.Document<unknown, {}, IPlaylist, {}> & IPlaylist & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=playlist.model.d.ts.map