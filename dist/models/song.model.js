import mongoose, { Schema } from "mongoose";
const songSchema = new Schema({
    title: {
        type: String,
        required: true,
    },
    artist: {
        type: String,
        required: true,
    },
    imageUrl: {
        type: String,
        required: true,
    },
    audioUrl: {
        type: String,
        required: true,
    },
    duration: {
        type: Number,
        required: true,
    },
    releaseDate: {
        type: Date,
        required: true,
        default: Date.now,
    },
    albumId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Album",
        required: false,
    },
    artistId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Artist",
        required: false,
    },
    featuredArtists: {
        type: [String],
        required: false,
        default: [],
    },
    credits: [{
            role: { type: String, required: true },
            name: { type: String, required: true },
            artistId: { type: mongoose.Schema.Types.ObjectId, ref: "Artist", required: false }
        }],
    composer: {
        type: String,
        required: false,
    },
    producer: {
        type: String,
        required: false,
    },
    source: {
        type: String,
        required: false,
    },
    bgColor: {
        type: String,
        required: false,
    },
    playCount: {
        type: Number,
        required: false,
        default: 0,
    },
    likeCount: {
        type: Number,
        required: false,
        default: 0,
    },
    lastPlayedAt: {
        type: Date,
        required: false,
    },
}, { timestamps: true });
export const Song = mongoose.model("Song", songSchema);
//# sourceMappingURL=song.model.js.map