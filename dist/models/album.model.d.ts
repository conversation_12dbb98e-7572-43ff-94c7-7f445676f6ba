import mongoose, { Document } from "mongoose";
export interface IAlbum extends Document {
    title: string;
    artist: string;
    imageUrl: string;
    releaseYear: number;
    genre: string;
    bgColor?: string;
    songs: mongoose.Types.ObjectId[];
    artistId?: mongoose.Types.ObjectId;
}
export declare const Album: mongoose.Model<IAlbum, {}, {}, {}, mongoose.Document<unknown, {}, IAlbum, {}> & IAlbum & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=album.model.d.ts.map