import mongoose, { Document } from "mongoose";
export interface IUserLike extends Document {
    userId: mongoose.Types.ObjectId;
    targetType: 'song' | 'artist';
    targetId: mongoose.Types.ObjectId;
}
export declare const UserLike: mongoose.Model<IUserLike, {}, {}, {}, mongoose.Document<unknown, {}, IUserLike, {}> & IUserLike & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=userLike.model.d.ts.map