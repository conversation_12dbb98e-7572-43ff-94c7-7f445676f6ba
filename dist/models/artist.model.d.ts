import mongoose, { Document } from "mongoose";
export interface IArtist extends Document {
    name: string;
    imageUrl: string;
    bgColor?: string;
    about?: string;
    monthlyListeners?: number;
    genres?: string[];
    socialLinks?: {
        spotify?: string;
        instagram?: string;
        twitter?: string;
        website?: string;
    };
    totalPlays: number;
    totalLikes: number;
    followerCount: number;
    lastPlayedAt?: Date;
}
export declare const Artist: mongoose.Model<IArtist, {}, {}, {}, mongoose.Document<unknown, {}, IArtist, {}> & IArtist & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=artist.model.d.ts.map