import mongoose, { Document } from "mongoose";
export interface ISong extends Document {
    title: string;
    artist: string;
    imageUrl: string;
    audioUrl: string;
    bgColor?: string;
    duration: number;
    releaseDate: Date;
    albumId?: mongoose.Types.ObjectId;
    artistId?: mongoose.Types.ObjectId;
    featuredArtists?: string[];
    credits?: {
        role: string;
        name: string;
        artistId?: mongoose.Types.ObjectId;
    }[];
    composer?: string;
    producer?: string;
    source?: string;
    playCount: number;
    likeCount: number;
    lastPlayedAt?: Date;
}
export declare const Song: mongoose.Model<ISong, {}, {}, {}, mongoose.Document<unknown, {}, ISong, {}> & ISong & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=song.model.d.ts.map