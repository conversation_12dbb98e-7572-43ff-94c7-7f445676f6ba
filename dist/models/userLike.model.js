import mongoose, { Schema } from "mongoose";
const userLikeSchema = new Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    targetType: {
        type: String,
        enum: ['song', 'artist'],
        required: true,
    },
    targetId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        refPath: 'targetType',
    },
}, { timestamps: true });
// Compound index to ensure a user can only like a specific item once
userLikeSchema.index({ userId: 1, targetType: 1, targetId: 1 }, { unique: true });
// Index for aggregation queries
userLikeSchema.index({ targetId: 1, targetType: 1 });
export const UserLike = mongoose.model("UserLike", userLikeSchema);
//# sourceMappingURL=userLike.model.js.map