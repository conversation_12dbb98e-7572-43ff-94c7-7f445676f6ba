import mongoose, { Schema } from "mongoose";
const albumSchema = new Schema({
    title: { type: String, required: true },
    artist: { type: String, required: true },
    imageUrl: { type: String, required: true },
    releaseYear: { type: Number, required: true },
    genre: { type: String, required: true },
    bgColor: { type: String, required: false },
    songs: [{ type: mongoose.Schema.Types.ObjectId, ref: "Song" }],
    artistId: { type: mongoose.Schema.Types.ObjectId, ref: "Artist", required: false },
}, { timestamps: true }); //  createdAt, updatedAt
export const Album = mongoose.model("Album", albumSchema);
//# sourceMappingURL=album.model.js.map