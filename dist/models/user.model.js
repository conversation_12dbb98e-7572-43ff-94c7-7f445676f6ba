import mongoose, { Schema } from "mongoose";
const userSchema = new Schema({
    name: {
        type: String,
        required: true,
    },
    image: {
        type: String,
        required: true,
    },
    email: {
        type: String,
        required: true,
        unique: true,
    },
    emailVerified: {
        type: Boolean,
        default: false,
    },
}, {
    timestamps: true, // createdAt, updatedAt
    collection: 'user' // Explicitly set collection name to match Better Auth
});
export const User = mongoose.model("User", userSchema);
//# sourceMappingURL=user.model.js.map