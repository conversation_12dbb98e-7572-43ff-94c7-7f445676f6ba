import mongoose, { Schema } from "mongoose";
const artistSchema = new Schema({
    name: {
        type: String,
        required: true,
        unique: true,
    },
    imageUrl: {
        type: String,
        required: true,
    },
    bgColor: {
        type: String,
        required: false,
    },
    about: {
        type: String,
        required: false,
    },
    monthlyListeners: {
        type: Number,
        required: false,
        default: 0,
    },
    genres: {
        type: [String],
        required: false,
    },
    socialLinks: {
        spotify: { type: String, required: false },
        instagram: { type: String, required: false },
        twitter: { type: String, required: false },
        website: { type: String, required: false },
    },
    totalPlays: {
        type: Number,
        required: false,
        default: 0,
    },
    totalLikes: {
        type: Number,
        required: false,
        default: 0,
    },
    followerCount: {
        type: Number,
        required: false,
        default: 0,
    },
    lastPlayedAt: {
        type: Date,
        required: false,
    },
}, { timestamps: true });
export const Artist = mongoose.model("Artist", artistSchema);
//# sourceMappingURL=artist.model.js.map