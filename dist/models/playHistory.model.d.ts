import mongoose, { Document } from "mongoose";
export interface IPlayHistory extends Document {
    userId: mongoose.Types.ObjectId;
    songId: mongoose.Types.ObjectId;
    artistId: mongoose.Types.ObjectId;
    playedAt: Date;
    duration: number;
    completed: boolean;
    sessionId?: string;
}
export declare const PlayHistory: mongoose.Model<IPlayHistory, {}, {}, {}, mongoose.Document<unknown, {}, IPlayHistory, {}> & IPlayHistory & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=playHistory.model.d.ts.map