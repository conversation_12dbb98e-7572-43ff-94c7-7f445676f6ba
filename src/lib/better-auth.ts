import { betterAuth } from "better-auth";
import { mongodbAdapter } from "better-auth/adapters/mongodb";
import { google } from "better-auth/social-providers";
import { jwt } from "better-auth/plugins/jwt";
import mongoose from "mongoose";

// Better Auth configuration
export const auth = betterAuth({
  // Required environment variables
  secret: process.env.BETTER_AUTH_SECRET!,
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:5000",
  
  // MongoDB adapter using native MongoDB connection
  database: mongodbAdapter(mongoose.connection.db!, {
    debugLogs: process.env.NODE_ENV === "development",
  }),
  
  // OAuth providers (replacing <PERSON>'s Google OAuth)
  socialProviders: {
    google: {
      prompt: "select_account",
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    },
  },
  
  // JWT plugin for session management
  plugins: [
    jwt(),
  ],
  
  // Callbacks
  callbacks: {
    onSignIn: async ({ user, account }: { user: any; account: any }) => {
      // Handle sign in logic
      console.log("User signed in:", user.email);
      return true;
    },
    
    onUserCreate: async ({ user }: { user: any }) => {
      // Handle user creation logic
      console.log("User created:", user.email);
      return true;
    },
  },
});

// Export the auth handler for Express
export const authHandler = auth.handler; 