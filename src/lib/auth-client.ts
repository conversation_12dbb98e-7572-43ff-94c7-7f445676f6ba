import { createAuthClient } from "better-auth/react";

// Create Better Auth client for React
export const authClient = createAuthClient({
  baseURL: `${import.meta.env.VITE_BACKEND_BASE_URL || "http://localhost:5000"}/api/auth`, // Your backend URL with auth path
});

// Export hooks and methods for easy access
export const { useSession } = authClient;

// Export sign-in methods using the correct API
export const signInWithGoogle = (options?: any) => 
  authClient.signIn.social({ provider: "google", ...options });

export const signOut = () => authClient.signOut(); 