import mongoose from "mongoose";
import { MongoClient, Db } from "mongodb";

export const connectDB = async (): Promise<void> => {
	try {
		const conn = await mongoose.connect(process.env.MONGODB_URI!);
		console.log(`Connected to MongoDB ${conn.connection.host}`);
		// Also connect native MongoDB client for Better Auth
		await connectNativeMongo();
	} catch (error) {
		console.log("Failed to connect to MongoDB", error);
		process.exit(1); // 1 is failure, 0 is success
	}
};

// Native MongoDB client for Better Auth
let nativeClient: MongoClient | null = null;
let nativeDb: Db | null = null;

export const connectNativeMongo = async () => {
	if (!nativeClient) {
		nativeClient = new MongoClient(process.env.MONGODB_URI!);
		await nativeClient.connect();
		
		// Extract database name from MongoDB URI
		const uri = process.env.MONGODB_URI!;
		const match = uri.match(/\/([^/?]+)(\?|$)/);
		const dbName = match ? match[1] : 'test';
		
		nativeDb = nativeClient.db(dbName);
		console.log("Native MongoDB client connected for Better Auth");
	}
};

export const getNativeMongoDb = (): Db => {
	if (!nativeDb) {
		throw new Error("Native MongoDB client not connected. Call connectNativeMongo() first.");
	}
	return nativeDb;
};

export const closeNativeMongo = async () => {
	if (nativeClient) {
		await nativeClient.close();
		nativeClient = null;
		nativeDb = null;
	}
};