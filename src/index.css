@import "./styles/design-system.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

*{
	font-family: "TikTok Sans", sans-serif;
}

@layer base {
	:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 14.9020%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 14.9020%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 14.9020%;
  --primary: 43.5000 67.7966% 53.7255%;
  --primary-foreground: 0 0% 0%;
  --secondary: 220.0000 14.2857% 95.8824%;
  --secondary-foreground: 215 13.7931% 34.1176%;
  --muted: 210 20.0000% 98.0392%;
  --muted-foreground: 220 8.9362% 46.0784%;
  --accent: 48.0000 100.0000% 96.0784%;
  --accent-foreground: 22.7273 82.5000% 31.3725%;
  --destructive: 0 84.2365% 60.1961%;
  --destructive-foreground: 0 0% 100%;
  --border: 220 13.0435% 90.9804%;
  --input: 220 13.0435% 90.9804%;
  --ring: 37.6923 92.1260% 50.1961%;
  --chart-1: 37.6923 92.1260% 50.1961%;
  --chart-2: 32.1327 94.6188% 43.7255%;
  --chart-3: 25.9649 90.4762% 37.0588%;
  --chart-4: 22.7273 82.5000% 31.3725%;
  --chart-5: 21.7143 77.7778% 26.4706%;
  --sidebar: 210 20.0000% 98.0392%;
  --sidebar-foreground: 0 0% 14.9020%;
  --sidebar-primary: 37.6923 92.1260% 50.1961%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 48.0000 100.0000% 96.0784%;
  --sidebar-accent-foreground: 22.7273 82.5000% 31.3725%;
  --sidebar-border: 220 13.0435% 90.9804%;
  --sidebar-ring: 37.6923 92.1260% 50.1961%;
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: 0 0% 9.0196%;
  --foreground: 0 0% 89.8039%;
  --card: 0 0% 14.9020%;
  --card-foreground: 0 0% 89.8039%;
  --popover: 0 0% 14.9020%;
  --popover-foreground: 0 0% 89.8039%;
  --primary: 37.6923 92.1260% 50.1961%;
  --primary-foreground: 0 0% 0%;
  --secondary: 0 0% 14.9020%;
  --secondary-foreground: 0 0% 89.8039%;
  --muted: 0 0% 14.9020%;
  --muted-foreground: 0 0% 63.9216%;
  --accent: 22.7273 82.5000% 31.3725%;
  --accent-foreground: 48 96.6387% 76.6667%;
  --destructive: 0 84.2365% 60.1961%;
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 25.0980%;
  --input: 0 0% 25.0980%;
  --ring: 37.6923 92.1260% 50.1961%;
  --chart-1: 43.2558 96.4126% 56.2745%;
  --chart-2: 32.1327 94.6188% 43.7255%;
  --chart-3: 22.7273 82.5000% 31.3725%;
  --chart-4: 25.9649 90.4762% 37.0588%;
  --chart-5: 22.7273 82.5000% 31.3725%;
  --sidebar: 0 0% 5.8824%;
  --sidebar-foreground: 0 0% 89.8039%;
  --sidebar-primary: 37.6923 92.1260% 50.1961%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 22.7273 82.5000% 31.3725%;
  --sidebar-accent-foreground: 48 96.6387% 76.6667%;
  --sidebar-border: 0 0% 25.0980%;
  --sidebar-ring: 37.6923 92.1260% 50.1961%;
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.375rem;
  --shadow-2xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 4px 8px -1px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 1px 2px -2px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 2px 4px -2px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 4px 6px -2px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.10), 0px 8px 10px -2px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 4px 8px -1px hsl(0 0% 0% / 0.25);
}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}
