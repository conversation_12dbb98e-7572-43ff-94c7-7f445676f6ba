import mongoose, { Document, Schema } from "mongoose";

export interface IMessage extends Document {
	senderId: string; // Clerk user ID
	receiverId: string; // Clerk user ID
	content: string;
}

const messageSchema = new Schema<IMessage>(
	{
		senderId: { type: String, required: true }, // Clerk user ID
		receiverId: { type: String, required: true }, // Clerk user ID
		content: { type: String, required: true },
	},
	{ timestamps: true }
);

export const Message = mongoose.model<IMessage>("Message", messageSchema);
