import mongoose, { Document, Schema } from "mongoose";

export interface IArtist extends Document {
	name: string;
	imageUrl: string;
	bgColor?: string;
	about?: string;
	monthlyListeners?: number;
	genres?: string[];
	socialLinks?: {
		spotify?: string;
		instagram?: string;
		twitter?: string;
		website?: string;
	};
	// Engagement fields
	totalPlays: number;
	totalLikes: number;
	followerCount: number;
	lastPlayedAt?: Date;
}

const artistSchema = new Schema<IArtist>(
	{
		name: {
			type: String,
			required: true,
			unique: true,
		},
		imageUrl: {
			type: String,
			required: true,
		},
		bgColor: {
			type: String,
			required: false,
		},
		about: {
			type: String,
			required: false,
		},
		monthlyListeners: {
			type: Number,
			required: false,
			default: 0,
		},
		genres: {
			type: [String],
			required: false,
		},
		socialLinks: {
			spotify: { type: String, required: false },
			instagram: { type: String, required: false },
			twitter: { type: String, required: false },
			website: { type: String, required: false },
		},
		totalPlays: {
			type: Number,
			required: false,
			default: 0,
		},
		totalLikes: {
			type: Number,
			required: false,
			default: 0,
		},
		followerCount: {
			type: Number,
			required: false,
			default: 0,
		},
		lastPlayedAt: {
			type: Date,
			required: false,
		},
	},
	{ timestamps: true }
);

export const Artist = mongoose.model<IArtist>("Artist", artistSchema);
