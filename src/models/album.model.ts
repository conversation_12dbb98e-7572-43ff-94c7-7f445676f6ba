import mongoose, { Document, Schema } from "mongoose";

export interface IAlbum extends Document {
	title: string;
	artist: string;
	imageUrl: string;
	releaseYear: number;
	genre: string;
	bgColor?: string;
	songs: mongoose.Types.ObjectId[];
	artistId?: mongoose.Types.ObjectId;
}

const albumSchema = new Schema<IAlbum>(
	{
		title: { type: String, required: true },
		artist: { type: String, required: true },
		imageUrl: { type: String, required: true },
		releaseYear: { type: Number, required: true },
		genre: { type: String, required: true },
		bgColor: { type: String, required: false },
		songs: [{ type: mongoose.Schema.Types.ObjectId, ref: "Song" }],
		artistId: { type: mongoose.Schema.Types.ObjectId, ref: "Artist", required: false },
	},
	{ timestamps: true }
); //  createdAt, updatedAt

export const Album = mongoose.model<IAlbum>("Album", albumSchema);
