import mongoose, { Document, Schema } from "mongoose";

export interface IUser extends Document {
	name: string;
	image: string;
	email: string;
	emailVerified?: boolean;
}

const userSchema = new Schema<IUser>(
	{
		name: {
			type: String,
			required: true,
		},
		image: {
			type: String,
			required: true,
		},
		email: {
			type: String,
			required: true,
			unique: true,
		},
		emailVerified: {
			type: Boolean,
			default: false,
		},
	},
	{
		timestamps: true, // createdAt, updatedAt
		collection: 'user' // Explicitly set collection name to match Better Auth
	}
);

export const User = mongoose.model<IUser>("User", userSchema);
