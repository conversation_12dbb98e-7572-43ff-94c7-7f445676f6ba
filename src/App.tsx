import { Route, Routes } from "react-router-dom";
import HomePage from "./pages/home/<USER>";
import AuthCallbackPage from "./pages/auth-callback/AuthCallbackPage";
import SignInPage from "./pages/sign-in/SignInPage";
import MainLayout from "./layout/MainLayout";
import AlbumPage from "./pages/album/AlbumPage";
import AdminPage from "./pages/admin/AdminPage";
import SearchPage from "./pages/search/SearchPage";
import LibraryPage from "./pages/library/LibraryPage";

import { Toaster } from "react-hot-toast";
import NotFoundPage from "./pages/404/NotFoundPage";
import SongPage from "./pages/song/songPage";
import ArtistPage from "./pages/artist/artistPage";
import PlaylistPage from "./pages/playlist/playlistPage";
import LikedSongsPage from "./pages/liked/LikedSongsPage";

function App() {
  return (
    <>
      <Routes>
        <Route path="/sign-in" element={<SignInPage />} />
        <Route path="/auth-callback" element={<AuthCallbackPage />} />
        <Route path="/admin" element={<AdminPage />} />

        <Route element={<MainLayout />}>
          <Route path="/" element={<HomePage />} />
          <Route path="/albums/:albumId" element={<AlbumPage />} />
          <Route path="/song/:songId" element={<SongPage />} />
          <Route path="/artist/:artistId" element={<ArtistPage />} />
          <Route path="/playlist/:playlistId" element={<PlaylistPage />} />
          <Route path="/liked" element={<LikedSongsPage />} />
          <Route path="/search" element={<SearchPage />} />
          <Route path="/library" element={<LibraryPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Route>
      </Routes>
      <Toaster />
    </>
  );
}

export default App;
