/**
 * Get the duration of an audio file in seconds
 * @param audioFile - The audio file to analyze
 * @returns Promise that resolves to the duration in seconds
 */
export const getAudioDuration = (audioFile: File): Promise<number> => {
	return new Promise((resolve, reject) => {
		const audio = new Audio();
		const objectUrl = URL.createObjectURL(audioFile);
		
		audio.addEventListener('loadedmetadata', () => {
			URL.revokeObjectURL(objectUrl);
			resolve(Math.floor(audio.duration));
		});
		
		audio.addEventListener('error', (error) => {
			URL.revokeObjectURL(objectUrl);
			reject(error);
		});
		
		audio.src = objectUrl;
	});
};

/**
 * Format duration in seconds to MM:SS format
 * @param seconds - Duration in seconds
 * @returns Formatted time string
 */
export const formatDuration = (seconds: number): string => {
	const minutes = Math.floor(seconds / 60);
	const remainingSeconds = seconds % 60;
	return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};
