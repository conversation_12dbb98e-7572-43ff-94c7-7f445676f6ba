import Topbar from "@/components/Topbar";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>rollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useMusicStore } from "@/stores/useMusicStore";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { Album, Song, Artist } from "@/types";
import {
  Clock,
  Pause,
  Play,
  CheckCircle,
  Heart,
  Share2,
  MoreHorizontal,
} from "lucide-react";
import { useEffect, useState, useMemo } from "react";
import { Link, useParams } from "react-router-dom";
import { FollowButton } from "@/components/ui/FollowButton";

export const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

export const formatPlayCount = (count: number) => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
};

const ArtistPage = () => {
  const { artistId } = useParams();
  const {
    fetchCurrentArtsist,
    currentArtist,
    isLoading,
    songs,
    albums,
    fetchSongs,
    fetchAlbums,
  } = useMusicStore();
  const { currentSong, isPlaying, playAlbum, togglePlay } = usePlayerStore();
  const [showAllTracks, setShowAllTracks] = useState(false);

  useEffect(() => {
    if (artistId) {
      fetchCurrentArtsist(artistId);
      fetchSongs();
      fetchAlbums();
    }
  }, [fetchCurrentArtsist, artistId, fetchSongs, fetchAlbums]);

  // Filter songs and albums by current artist
  const artistSongs = useMemo(() => {
    return songs
      .filter((song) => song.artistId === artistId)
      .sort((a, b) => (b.playCount || 0) - (a.playCount || 0));
  }, [songs, artistId]);

  const artistAlbums = useMemo(() => {
    return albums
      .filter((album) => album.artistId === artistId)
      .sort((a, b) => b.releaseYear - a.releaseYear);
  }, [albums, artistId]);

  const collaborations = useMemo(() => {
    // Find albums where the artist appears but isn't the main artist
    return albums.filter(
      (album) =>
        album.artistId !== artistId &&
        album.songs.some(
          (song) =>
            song.artistId === artistId ||
            song.featuredArtists?.includes(currentArtist?.name || "")
        )
    );
  }, [albums, artistId, currentArtist]);

  // Get popular tracks (limit to 5 initially, show more if requested)
  const popularTracks = useMemo(() => {
    const tracks = artistSongs.slice(0, showAllTracks ? artistSongs.length : 5);
    return tracks;
  }, [artistSongs, showAllTracks]);

  if (isLoading) return null;
  if (!currentArtist) return <div>Artist not found</div>;

  const handlePlayArtistSongs = () => {
    if (artistSongs.length === 0) return;

    const isCurrentArtistPlaying = artistSongs.some(
      (song) => song._id === currentSong?._id
    );
    if (isCurrentArtistPlaying) {
      togglePlay();
    } else {
      playAlbum(artistSongs, 0);
    }
  };

  const handlePlaySong = (song: Song, index: number) => {
    const isCurrentSong = currentSong?._id === song._id;
    if (isCurrentSong && isPlaying) {
      togglePlay();
    } else {
      playAlbum(artistSongs, index);
    }
  };

  return (
    <div className="h-full">
      <ScrollArea className="h-[95%] lg:h-[90%] md:h-full rounded-md">
        {/* Main Content */}
        <div className="relative min-h-full">
          {/* Hero Section */}
          <div className="relative">
            {/* Background gradient */}
            <div
              className="absolute inset-0 bg-gradient-to-b pointer-events-none"
              aria-hidden="true"
              style={{
                background: currentArtist?.bgColor
                  ? `linear-gradient(to bottom, ${currentArtist.bgColor}CC, ${currentArtist.bgColor}33, transparent)`
                  : "linear-gradient(to bottom, #1a1a1a, #2a2a2a33, transparent)",
              }}
            />

            {/* Hero Content */}
            <div className="relative z-10 flex flex-col md:flex-row p-6 gap-6 pb-8 mt-5 md:mt-0">
              <img
                src={currentArtist.imageUrl}
                alt={currentArtist.name}
                className="w-[240px] h-[240px] shadow-xl rounded-full object-cover mx-auto"
              />
              <div className="flex flex-col justify-end">
                {/* Verification Badge */}
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium text-muted-foreground">
                    Verified Artist
                  </span>
                </div>

                {/* Artist Name */}
                <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold mb-4 leading-none">
                  {currentArtist.name}
                </h1>

                {/* Monthly Listeners */}
                <div className="text-base text-muted-foreground">
                  {currentArtist.monthlyListeners
                    ? `${formatPlayCount(
                        currentArtist.monthlyListeners
                      )} monthly listeners`
                    : `${artistSongs.length} songs`}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="relative z-10 px-6 pb-6 flex items-center gap-4">
              <Button
                onClick={handlePlayArtistSongs}
                size="icon"
                className="w-14 h-14 rounded-full hover:scale-105 transition-all"
                disabled={artistSongs.length === 0}
              >
                {isPlaying &&
                artistSongs.some((song) => song._id === currentSong?._id) ? (
                  <Pause className="h-7 w-7" />
                ) : (
                  <Play className="h-7 w-7" />
                )}
              </Button>
              <FollowButton
                artistId={currentArtist._id}
                variant="outline"
                size="sm"
                className="px-6"
              />
              {/* <Button variant="ghost" size="icon" className="rounded-full">
                <MoreHorizontal className="h-5 w-5" />
              </Button> */}
            </div>
          </div>

          {/* Content Sections */}
          <div className="relative z-10 px-6 pb-8 space-y-12">
            {/* Popular Tracks Section */}
            {artistSongs.length > 0 && (
              <section>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold">Popular</h2>
                  {artistSongs.length > 5 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowAllTracks(!showAllTracks)}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      {showAllTracks ? "Show less" : "Show all"}
                    </Button>
                  )}
                </div>

                {/* Tracks List */}
                <div className="space-y-2">
                  {popularTracks.map((song, index) => {
                    const isCurrentSong = currentSong?._id === song._id;
                    const isTrackPlaying = isCurrentSong && isPlaying;

                    return (
                      <div
                        key={song._id}
                        onClick={() => handlePlaySong(song, index)}
                        className="grid grid-cols-[16px_4fr_2fr_1fr] gap-4 px-4 py-2 text-sm hover:bg-accent/50 rounded-md group cursor-pointer items-center"
                      >
                        <div className="flex items-center justify-center">
                          {isTrackPlaying ? (
                            <div className="size-4 text-primary">♫</div>
                          ) : (
                            <span
                              className={`group-hover:hidden ${
                                isCurrentSong ? "text-primary" : ""
                              }`}
                            >
                              {index + 1}
                            </span>
                          )}
                          {!isTrackPlaying && (
                            <Play className="h-4 w-4 hidden group-hover:block" />
                          )}
                        </div>

                        <div className="flex items-center gap-3">
                          <img
                            src={song.imageUrl}
                            alt={song.title}
                            className="size-10 rounded"
                          />
                          <div>
                            <div
                              className={`font-medium ${
                                isCurrentSong ? "text-primary" : ""
                              }`}
                            >
                              {song.title}
                            </div>
                            <div className="text-muted-foreground">
                              {song.artist}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center text-muted-foreground">
                          {song.playCount
                            ? formatPlayCount(song.playCount)
                            : "-"}
                        </div>

                        <div className="flex items-center text-muted-foreground">
                          {formatDuration(song.duration)}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </section>
            )}

            {/* Discography Section */}
            {artistAlbums.length > 0 && (
              <section>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold">Discography</h2>
                  {artistAlbums.length > 6 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-muted-foreground hover:text-foreground"
                    >
                      Show all
                    </Button>
                  )}
                </div>

                <Tabs defaultValue="all" className="w-full">
                  <TabsList className="grid grid-cols-3 w-fit mb-6">
                    <TabsTrigger value="all">Popular releases</TabsTrigger>
                    <TabsTrigger value="albums">Albums</TabsTrigger>
                    <TabsTrigger value="singles">Singles and EPs</TabsTrigger>
                  </TabsList>

                  <TabsContent value="all" className="mt-0">
                    <ScrollArea className="h-full">
                      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
                        {artistAlbums.slice(0, 8).map((album) => (
                          <div
                            key={album._id}
                            className="group cursor-pointer hover:bg-accent/50 transition-all max-w-md shadow-none"
                          >
                            <Link to={`/albums/${album._id}`}>
                              <div className="">
                                <div className="aspect-square mb-4">
                                  <img
                                    src={album.imageUrl}
                                    alt={album.title}
                                    className="w-full h-full object-cover rounded-md shadow-md group-hover:shadow-lg transition-shadow"
                                  />
                                </div>
                                <div>
                                  <h3 className="font-medium text-sm line-clamp-1 mb-1 group-hover:text-primary group-hover:underline">
                                    {album.title}
                                  </h3>
                                  <p className="text-xs text-muted-foreground">
                                    {album.releaseYear} • Album
                                  </p>
                                </div>
                              </div>
                            </Link>
                          </div>
                        ))}
                      </div>
                      <ScrollBar orientation="horizontal" />
                    </ScrollArea>
                  </TabsContent>

                  <TabsContent value="albums">
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
                      {!artistAlbums.length && (
                        <div className="text-muted-foreground text-center py-8">
                          No albums available
                        </div>
                      )}
                      {artistAlbums.slice(0, 8).map((album) => (
                        <div
                          key={album._id}
                          className="group cursor-pointer hover:bg-accent/50 transition-all max-w-md shadow-none"
                        >
                          <Link to={`/albums/${album._id}`}>
                            <div className="">
                              <div className="aspect-square mb-4">
                                <img
                                  src={album.imageUrl}
                                  alt={album.title}
                                  className="w-full h-full object-cover rounded-md shadow-md group-hover:shadow-lg transition-shadow"
                                />
                              </div>
                              <div>
                                <h3 className="font-medium text-sm line-clamp-1 mb-1 group-hover:text-primary group-hover:underline">
                                  {album.title}
                                </h3>
                                <p className="text-xs text-muted-foreground">
                                  {album.releaseYear} • Album
                                </p>
                              </div>
                            </div>
                          </Link>
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="singles">
                    <div className="text-muted-foreground text-center py-8">
                      No singles or EPs available
                    </div>
                  </TabsContent>
                </Tabs>
              </section>
            )}

            {/* About Section */}
            {currentArtist.about && (
              <section>
                <h2 className="text-2xl font-bold mb-4">About</h2>
                <div className="max-w-2xl">
                  <p className="text-muted-foreground leading-relaxed">
                    {currentArtist.about}
                  </p>
                </div>
              </section>
            )}

            {/* Appears On Section */}
            {collaborations.length > 0 && (
              <section>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold">Appears On</h2>
                  {collaborations.length > 6 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-muted-foreground hover:text-foreground"
                    >
                      Show all
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-7 gap-4">
                  {collaborations.slice(0, 7).map((album) => (
                    <Card
                      key={album._id}
                      className="group cursor-pointer hover:bg-accent/50 transition-all"
                    >
                      <CardContent className="p-4">
                        <div className="aspect-square mb-4">
                          <img
                            src={album.imageUrl}
                            alt={album.title}
                            className="w-full h-full object-cover rounded-md shadow-md group-hover:shadow-lg transition-shadow"
                          />
                        </div>
                        <div>
                          <h3 className="font-medium text-sm line-clamp-1 mb-1">
                            {album.title}
                          </h3>
                          <p className="text-xs text-muted-foreground line-clamp-1">
                            {album.artist}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {album.releaseYear}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </section>
            )}
          </div>
        </div>
      </ScrollArea>
    </div>
  );
};

export default ArtistPage;
