import { useState, useEffect, useMemo } from "react";
import { useSearchParams } from "react-router-dom";
import { useMusicStore } from "@/stores/useMusicStore";
import { usePlaylistStore } from "@/stores/usePlaylistStore";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { ScrollArea } from "@/components/ui/scroll-area";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import SearchHeader from "@/components/search/SearchHeader";
import SearchResults from "@/components/search/SearchResults";
import { PlaylistSelectionModal } from "@/components/PlaylistSelectionModal";
import { Song } from "@/types";

type SearchCategory = "all" | "songs" | "artists" | "albums" | "playlists";

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams.get("q") || "");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchParams.get("q") || "");
  const [activeCategory, setActiveCategory] = useState<SearchCategory>("all");
  const [selectedSongForPlaylist, setSelectedSongForPlaylist] = useState<string | null>(null);
  const [isPlaylistModalOpen, setIsPlaylistModalOpen] = useState(false);

  const {
    songs,
    albums,
    artists,
    fetchSongs,
    fetchAlbums,
    fetchArtists,
    isLoadingSongs,
    isLoadingAlbums,
    isLoadingArtists
  } = useMusicStore();

  const {
    userPlaylists,
    publicPlaylists,
    fetchUserPlaylists,
    fetchPublicPlaylists,
    isLoading: isLoadingPlaylists
  } = usePlaylistStore();

  const {
    currentSong,
    setCurrentSong,
    togglePlay
  } = usePlayerStore();

  const { trackSongPlay } = useEngagementStore();

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300); // 300ms debounce delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  useEffect(() => {
    // Fetch all data when component mounts - only run once
    fetchSongs();
    fetchAlbums();
    fetchArtists();
    fetchUserPlaylists();
    fetchPublicPlaylists();
  }, []); // Empty dependency array to prevent infinite loops

  useEffect(() => {
    // Update search query from URL params
    const query = searchParams.get("q");
    if (query) {
      setSearchQuery(query);
      setDebouncedSearchQuery(query); // Set immediately for URL params
    }
  }, [searchParams]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setSearchParams({ q: searchQuery.trim() });
    } else {
      setSearchParams({});
    }
  };

  const handlePlay = (song: Song) => {
    if (currentSong?._id === song._id) {
      togglePlay();
    } else {
      setCurrentSong(song);
      trackSongPlay(song._id);
    }
  };

  const handleAddToPlaylist = (songId: string) => {
    setSelectedSongForPlaylist(songId);
    setIsPlaylistModalOpen(true);
  };

  // Filter results based on debounced search query
  const filteredResults = useMemo(() => {
    if (!debouncedSearchQuery.trim()) {
      return {
        songs: [],
        artists: [],
        albums: [],
        playlists: []
      };
    }

    const query = debouncedSearchQuery.toLowerCase();

    const filteredSongs = songs.filter(song =>
      song.title.toLowerCase().includes(query) ||
      song.artist.toLowerCase().includes(query)
    );

    const filteredArtists = artists.filter(artist =>
      artist.name.toLowerCase().includes(query)
    );

    const filteredAlbums = albums.filter(album =>
      album.title.toLowerCase().includes(query) ||
      album.artist.toLowerCase().includes(query)
    );

    const allPlaylists = [...userPlaylists, ...publicPlaylists];
    const filteredPlaylists = allPlaylists.filter(playlist =>
      playlist.name.toLowerCase().includes(query) ||
      (playlist.description && playlist.description.toLowerCase().includes(query))
    );

    return {
      songs: filteredSongs,
      artists: filteredArtists,
      albums: filteredAlbums,
      playlists: filteredPlaylists
    };
  }, [debouncedSearchQuery, songs, artists, albums, userPlaylists, publicPlaylists]);

  const categories = [
    { key: "all" as const, label: "All", count: Object.values(filteredResults).flat().length },
    { key: "songs" as const, label: "Songs", count: filteredResults.songs.length },
    { key: "artists" as const, label: "Artists", count: filteredResults.artists.length },
    { key: "albums" as const, label: "Albums", count: filteredResults.albums.length },
    { key: "playlists" as const, label: "Playlists", count: filteredResults.playlists.length },
  ];

  const getDisplayResults = () => {
    switch (activeCategory) {
      case "songs":
        return { songs: filteredResults.songs, artists: [], albums: [], playlists: [] };
      case "artists":
        return { songs: [], artists: filteredResults.artists, albums: [], playlists: [] };
      case "albums":
        return { songs: [], artists: [], albums: filteredResults.albums, playlists: [] };
      case "playlists":
        return { songs: [], artists: [], albums: [], playlists: filteredResults.playlists };
      default:
        return filteredResults;
    }
  };

  const displayResults = getDisplayResults();

  // Show loading spinner only if any of the initial data is still loading
  const isInitialLoading = isLoadingSongs || isLoadingAlbums || isLoadingArtists || isLoadingPlaylists;

  if (isInitialLoading) {
    return <LoadingSpinner message="Loading..." />;
  }

  return (
    <div className="h-full bg-white mt-5 md:mt-0">
      <ScrollArea className="h-[90%]">
        <div className="p-6 space-y-6">
          {/* Search Header */}
          <SearchHeader
            searchQuery={searchQuery}
            onSearchQueryChange={setSearchQuery}
            onSearch={handleSearch}
            activeCategory={activeCategory}
            onCategoryChange={(category) => setActiveCategory(category as SearchCategory)}
            categories={categories}
          />

          {/* Search Results */}
          <SearchResults
            results={displayResults}
            activeCategory={activeCategory}
            searchQuery={debouncedSearchQuery}
            onPlay={handlePlay}
            onAddToPlaylist={handleAddToPlaylist}
            onCategoryChange={(category) => setActiveCategory(category as SearchCategory)}
          />

        </div>
      </ScrollArea>

      {/* Playlist Selection Modal */}
      {selectedSongForPlaylist && (
        <PlaylistSelectionModal
          isOpen={isPlaylistModalOpen}
          onClose={() => {
            setIsPlaylistModalOpen(false);
            setSelectedSongForPlaylist(null);
          }}
          songId={selectedSongForPlaylist}
          songTitle={songs.find(s => s._id === selectedSongForPlaylist)?.title}
        />
      )}
    </div>
  );
};

export default SearchPage;