import Topbar from "@/components/Topbar";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useMusicStore } from "@/stores/useMusicStore";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { Pause, Play } from "lucide-react";
import { useEffect } from "react";
import { useParams } from "react-router-dom";

export const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const SongPage = () => {
  const { songId } = useParams();
  const {
    fetchSongById,
    currentSong,
    isLoading,
    currentArtist,
    fetchCurrentArtsist,
  } = useMusicStore();
  const {
    isPlaying,
    playAlbum,
    togglePlay,
    currentSong: playerCurrentSong,
  } = usePlayerStore();

  useEffect(() => {
    if (songId) fetchSongById(songId);
  }, [fetchSongById, songId]);

  useEffect(() => {
    if (currentSong?.artistId) fetchCurrentArtsist(currentSong.artistId);
  }, [fetchCurrentArtsist, currentSong?.artistId]);

  if (isLoading) return null;
  if (!currentSong) return <div className="p-6">Song not found</div>;

  const handlePlaySong = () => {
    const isCurrentSongPlaying = playerCurrentSong?._id === currentSong._id;
    if (isCurrentSongPlaying) {
      togglePlay();
    } else {
      // Play this single song
      playAlbum([currentSong], 0);
    }
  };

  const playCount = currentSong.playCount || 0;

  return (
    <>
    <ScrollArea className="h-full rounded-md">
      <div className="h-full">
        {/* Main Content */}
        <div className="relative min-h-full">
          {/* bg gradient */}
          <div
            className="absolute inset-0 bg-gradient-to-b pointer-events-none"
            aria-hidden="true"
            style={{
              background:
                currentArtist?.bgColor &&
                `linear-gradient(to bottom, ${currentArtist.bgColor}CC, rgba(244, 244, 245, 0.8), #f4f4f5)`,
            }}
          />

          {/* Content */}
          <div className="relative z-10">
            <div className="flex p-6 gap-6 pb-8">
              <img
                src={currentSong.imageUrl}
                alt={currentSong.title}
                className="w-[240px] h-[240px] shadow-xl rounded"
              />
              <div className="flex flex-col justify-end">
                <p className="text-sm font-medium">Song</p>
                <h1 className="text-4xl md:text-7xl font-bold my-4">
                  {currentSong.title}
                </h1>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <span className="font-medium text-black">
                    {currentSong.artist}
                  </span>
                  {currentSong.releaseDate && (
                    <>
                      <span>•</span>
                      <span>
                        {new Date(currentSong.releaseDate).getFullYear()}
                      </span>
                    </>
                  )}
                  <span>•</span>
                  <span>{formatDuration(currentSong.duration)}</span>
                  {playCount > 0 && (
                    <>
                      <span>•</span>
                      <span>{playCount.toLocaleString()} plays</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* play button */}
            <div className="px-6 pb-4 flex items-center gap-6">
              <Button
                onClick={handlePlaySong}
                size="icon"
                className="w-14 h-14 rounded-full
                hover:scale-105 transition-all"
              >
                {isPlaying && playerCurrentSong?._id === currentSong._id ? (
                  <Pause className="h-7 w-7 text-black" />
                ) : (
                  <Play className="h-7 w-7 text-black" />
                )}
              </Button>
            </div>

            {/* Song Details Section */}
            <div>
              <div className="px-6 py-6">
                <h2 className="text-xl font-semibold mb-4 text-black">
                  Song Details
                </h2>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-medium text-black">Artist</h3>
                      <p className="text-muted-foreground">
                        {currentSong.artist}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-medium text-black">Duration</h3>
                      <p className="text-muted-foreground">
                        {formatDuration(currentSong.duration)}
                      </p>
                    </div>
                    {currentSong.releaseDate && (
                      <div>
                        <h3 className="font-medium text-black">Release Date</h3>
                        <p className="text-muted-foreground">
                          {new Date(
                            currentSong.releaseDate
                          ).toLocaleDateString()}
                        </p>
                      </div>
                    )}
                    {playCount > 0 && (
                      <div>
                        <h3 className="font-medium text-black">Play Count</h3>
                        <p className="text-muted-foreground">
                          {playCount.toLocaleString()}
                        </p>
                      </div>
                    )}
                    {currentSong.composer && (
                      <div>
                        <h3 className="font-medium text-black">Composer</h3>
                        <p className="text-muted-foreground">
                          {currentSong.composer}
                        </p>
                      </div>
                    )}
                    {currentSong.producer && (
                      <div>
                        <h3 className="font-medium text-black">Producer</h3>
                        <p className="text-muted-foreground">
                          {currentSong.producer}
                        </p>
                      </div>
                    )}
                  </div>

                  {currentSong.featuredArtists &&
                    currentSong.featuredArtists.length > 0 && (
                      <div>
                        <h3 className="font-medium text-black">
                          Featured Artists
                        </h3>
                        <p className="text-muted-foreground">
                          {currentSong.featuredArtists.join(", ")}
                        </p>
                      </div>
                    )}

                  {currentSong.credits && currentSong.credits.length > 0 && (
                    <div>
                      <h3 className="font-medium text-black">Credits</h3>
                      <div className="space-y-1">
                        {currentSong.credits.map((credit, index) => (
                          <p key={index} className="text-muted-foreground">
                            <span className="font-medium">{credit.role}:</span>{" "}
                            {credit.name}
                          </p>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ScrollArea>
    </>
  );
};
export default SongPage;
