import { useMusicStore } from "@/stores/useMusicStore";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { LikeButton } from "@/components/ui/LikeButton";
import { FollowButton } from "@/components/ui/FollowButton";
import { Play, Clock, Music } from "lucide-react";
import { useEffect } from "react";

const HeroCard = () => {
  const { featuredSongs, isLoading, artists, fetchArtists } = useMusicStore();
  const { setCurrentSong } = usePlayerStore();
  const { trackSongPlay, songLikeCounts } = useEngagementStore();

  // Get the most popular song for the hero card
  const heroSong = featuredSongs[0];
  useEffect(() => {
    fetchArtists();
  }, [fetchArtists]);

  if (isLoading || !heroSong) return null;

  // Find the artist for the current song
  const currentArtist = artists.find(
    (artist) =>
      artist.name === heroSong.artist || artist._id === heroSong.artistId
  );

  const handlePlay = () => {
    setCurrentSong(heroSong);
    // Track the play
    trackSongPlay(heroSong._id);
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div
      className="relative mb-12 rounded-2xl overflow-hidden shadow-2xl"
      style={{
        background: currentArtist?.bgColor
          ? `linear-gradient(to bottom, transparent 0%, ${currentArtist.bgColor}40 70%, ${currentArtist.bgColor}80 100%)`
          : "linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 70%, rgba(0,0,0,0.6) 100%)",
        aspectRatio: "16/9",
        minHeight: "400px",
      }}
    >
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <img
          src={heroSong.imageUrl}
          alt={heroSong.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/20 to-transparent" />
      </div>

      {/* Content */}
      <div className="relative z-10 p-8 h-full flex flex-col justify-between">
        {/* Badge */}
        <div className="mb-6">
          <Badge
            variant="secondary"
            className="inline-flex items-center px-4 py-2 rounded-full text-xs font-medium uppercase tracking-wide bg-white/20 text-white backdrop-blur-sm border-0 hover:bg-white/30 transition-colors duration-200"
          >
            <Music className="w-3 h-3 mr-2" />
            Featured Track
          </Badge>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col justify-center">
          <h2
            className="text-4xl md:text-6xl font-bold text-white mb-4"
            style={{
              // fontFamily: "SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif",
              fontWeight: "700",
              lineHeight: "1.1",
              letterSpacing: "-0.025em",
            }}
          >
            {heroSong.title}
          </h2>
          <p
            className="text-xl md:text-2xl text-white/90 mb-6 max-w-2xl"
            style={{
              fontWeight: "400",
              lineHeight: "1.4",
            }}
          >
            by {heroSong.artist}
          </p>

          {/* Stats Row */}
          <div className="flex items-center gap-6 mb-8">
            <div className="flex items-center gap-2 text-white/80">
              <span className="text-sm font-medium">
                {heroSong.likeCount ? `${heroSong.likeCount.toLocaleString()} likes` : '0 likes'}
              </span>
            </div>
            <div className="flex items-center gap-2 text-white/80">
              <Music className="w-4 h-4" />
              <span className="text-sm font-medium">
                {heroSong.playCount ? `${heroSong.playCount.toLocaleString()} plays` : '0 plays'}
              </span>
            </div>
            <div className="flex items-center gap-2 text-white/80">
              <Clock className="w-4 h-4" />
              <span className="text-sm font-medium">
                {formatDuration(heroSong.duration || 210)}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-4">
            <Button
              onClick={handlePlay}
              className="bg-white text-black hover:bg-white/90 px-8 py-3 rounded-full font-semibold transition-all duration-300 hover:scale-105"
              style={{
                boxShadow: "0 8px 24px rgba(0,0,0,0.15)",
              }}
            >
              <Play className="w-5 h-5 mr-2" />
              Play Now
            </Button>

            <LikeButton
              songId={heroSong._id}
              variant="ghost"
              className="text-white hover:bg-white/20 p-3 rounded-full transition-all duration-300 hover:scale-105"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroCard;
