import Topbar from "@/components/Topbar";
import { useMusicStore } from "@/stores/useMusicStore";
import { useEffect, useState } from "react";
import FeaturedSection from "./components/FeaturedSection";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import SectionGrid from "./components/SectionGrid";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useSession } from "@/lib/auth-client";
import HeroCard from "./components/HeroCard";
import MobileHeroSection from "./components/MobileHeroSection";
import MobileFeaturedSection from "./components/MobileFeaturedSection";
import MobileSectionGrid from "./components/MobileSectionGrid";
import TabletFeaturedSection from "./components/TabletFeaturedSection";
import TabletSectionGrid from "./components/TabletSectionGrid";
import { PopularArtists } from "./components/PopularArtists";
import { MobilePopularArtists } from "./components/MobilePopularArtists";
import { TabletPopularArtists } from "./components/TabletPopularArtists";

const HomePage = () => {
  const [viewMode, setViewMode] = useState<"mobile" | "tablet" | "desktop">(
    "desktop"
  );

  const {
    fetchFeaturedSongs,
    fetchMadeForYouSongs,
    fetchTrendingSongs,
    isLoading,
    madeForYouSongs,
    featuredSongs,
    trendingSongs,
  } = useMusicStore();

  const { initializeQueue } = usePlayerStore();
  const { data: session } = useSession();

  useEffect(() => {
    const checkViewMode = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setViewMode("mobile");
      } else if (width < 1024) {
        setViewMode("tablet");
      } else {
        setViewMode("desktop");
      }
    };

    checkViewMode();
    window.addEventListener("resize", checkViewMode);
    return () => window.removeEventListener("resize", checkViewMode);
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 18) return "Good afternoon";
    return "Good evening";
  };

  // Mobile layout
  if (viewMode === "mobile") {
    return (
      <main className="h-full mt-5">
        <ScrollArea className="h-full">
          <div className="pb-8 mb-8">
            {/* Mobile Hero Section */}
            <MobileHeroSection />

            {/* Mobile Featured Section */}
            {/* <MobileFeaturedSection /> */}

            {/* Mobile Section Grids */}
            <div className="space-y-8">
              <MobileSectionGrid
                title="Made For You"
                songs={madeForYouSongs}
                isLoading={isLoading}
              />

              <MobileSectionGrid
                title="Trending"
                songs={trendingSongs}
                isLoading={isLoading}
              />
            </div>

            {/* Mobile Popular Artists */}
            <MobilePopularArtists />
          </div>
        </ScrollArea>
      </main>
    );
  }

  // Tablet layout - mirrors mobile design with flex-wrap
  if (viewMode === "tablet") {
    return (
      <main className="h-full bg-gray-50/30">
        <ScrollArea className="h-full">
          <div className="pb-8">
            {/* Tablet Hero Section - reuse mobile hero */}
            <section className="mb-12 mt-5">
              <HeroCard />
            </section>

            {/* Tablet Featured Section with flex-wrap */}
            {/* <TabletFeaturedSection /> */}

            {/* Tablet Section Grids with flex-wrap */}
            <div className="space-y-8">
              <TabletSectionGrid
                title="Made For You"
                songs={madeForYouSongs}
                isLoading={isLoading}
              />

              <TabletSectionGrid
                title="Trending"
                songs={trendingSongs}
                isLoading={isLoading}
              />
            </div>
            {/* Tablet Popular Artists */}
            <TabletPopularArtists />
          </div>
        </ScrollArea>
      </main>
    );
  }

  // Desktop layout
  return (
    <main className="rounded-md overflow-hidden h-full bg-white">
      <ScrollArea className="h-[calc(100vh-180px)]">
        <div
          className="p-6 space-y-8"
          style={{ maxWidth: "1200px", margin: "0 auto" }}
        >
          {/* Header Section */}
          <header className="mb-8">
            <h1
              className="text-5xl font-bold mb-2"
              style={{
                color: "#1d1d1f",
                fontWeight: "700",
                lineHeight: "1.1",
                letterSpacing: "-0.025em",
              }}
            >
              {getGreeting()}
            </h1>
            <p
              className="text-lg"
              style={{
                color: "#86868b",
                fontWeight: "400",
                lineHeight: "1.4",
                opacity: "0.8",
              }}
            >
              {session?.user
                ? `Welcome back, ${session.user.name || session.user.email}`
                : "Discover your next favorite song"}
            </p>
          </header>

          {/* Hero Section */}
          <section className="mb-12">
            <HeroCard />
          </section>

          {/* Quick Access Section */}
          {/* <section className="mb-12">
            <FeaturedSection />
          </section> */}

          {/* Visual Separator */}
          {/* <Separator className="my-8" style={{ backgroundColor: "#e8e8ea" }} /> */}

          {/* Content Grid Sections */}
          <section className="space-y-12">
            <SectionGrid
              title="Made For You"
              songs={madeForYouSongs}
              isLoading={isLoading}
            />

            <Separator
              className="my-8"
              style={{ backgroundColor: "#e8e8ea" }}
            />

            <SectionGrid
              title="Trending"
              songs={trendingSongs}
              isLoading={isLoading}
            />
          </section>

          {/* Popular Artists Section */}
          <section className="">
            <PopularArtists />
          </section>
        </div>
      </ScrollArea>
    </main>
  );
};
export default HomePage;
