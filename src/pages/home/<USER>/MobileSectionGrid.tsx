import { Song } from "@/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { LikeButton } from "@/components/ui/LikeButton";
import { Pause } from "lucide-react";
import HorizontalScrollSection from "@/components/ui/HorizontalScrollSection";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Link } from "react-router-dom";

type MobileSectionGridProps = {
  title: string;
  songs: Song[];
  isLoading: boolean;
};

const MobileSectionGrid = ({
  songs,
  title,
  isLoading,
}: MobileSectionGridProps) => {
  const { currentSong, isPlaying, setCurrentSong, togglePlay } =
    usePlayerStore();
  const { trackSongPlay } = useEngagementStore();

  if (isLoading) return <div className="px-4 mb-8">Loading...</div>;

  const handlePlay = (song: Song) => {
    if (currentSong?._id === song._id) {
      togglePlay();
    } else {
      setCurrentSong(song);
      // Track the play when a new song starts
      trackSongPlay(song._id);
    }
  };

  const handleShowAll = () => {
    // TODO: Navigate to full section page
    console.log(`Show all ${title} songs`);
  };

  return (
    <ScrollArea className="h-full max-w-[26rem]">
      <div className="flex w-full overflow-x-auto justify-center gap-4 px-4 pb-2">
        {songs.map((song) => {
          const isCurrentSong = currentSong?._id === song._id;
          const isCurrentlyPlaying = isCurrentSong && isPlaying;

          return (
            <div
              key={song._id}
              className="flex-shrink-0 w-40 cursor-pointer"
              onClick={() => handlePlay(song)}
            >
              <div className="relative mb-3">
                <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 shadow-sm">
                  <img
                    src={song.imageUrl}
                    alt={song.title}
                    className="w-full h-full object-cover"
                  />

                  {/* Play Button Overlay */}
                  {isCurrentlyPlaying && (
                    <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                      <Button
                        size="icon"
                        className="bg-white text-black hover:bg-white/90 w-12 h-12 rounded-full"
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePlay(song);
                        }}
                      >
                        <Pause className="w-5 h-5" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-gray-900 text-sm flex-1">
                    <Link
                      to={`/song/${song._id}`}
                      className="group-hover:text-primary group-hover:underline"
                    >
                      {song.title}
                    </Link>
                  </h3>
                  <LikeButton
                    songId={song._id}
                    size="sm"
                    variant="ghost"
                    className="ml-1"
                  />
                </div>
                <p className="text-xs text-gray-600">{song.artist}</p>
              </div>
            </div>
          );
        })}
      </div>
      <ScrollBar orientation="horizontal" />
    </ScrollArea>
  );
};

export default MobileSectionGrid;
