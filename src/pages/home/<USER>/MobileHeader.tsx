import { Search, Settings } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface MobileHeaderProps {
  greeting: string;
  session: any;
}

const MobileHeader = ({ greeting, session }: MobileHeaderProps) => {
  return (
    <header className="fixed top-0 left-0 right-0 h-16 bg-white/80 backdrop-blur-md border-b border-gray-200 z-50">
      <div className="flex items-center justify-between h-full px-4">
        <div className="flex-1 min-w-0">
          <h1 className="text-xl font-bold text-gray-900 truncate">
            {greeting}
          </h1>
          {session?.user && (
            <p className="text-sm text-gray-600 truncate">
              {session.user.name || session.user.email}
            </p>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="w-10 h-10 rounded-full"
          >
            <Search className="w-5 h-5" />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            className="w-10 h-10 rounded-full"
          >
            <Settings className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </header>
  );
};

export default MobileHeader;
