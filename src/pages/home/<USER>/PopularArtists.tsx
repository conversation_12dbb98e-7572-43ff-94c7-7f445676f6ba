import { useEffect } from "react";
import { useMusicStore } from "@/stores/useMusicStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { FollowButton } from "@/components/ui/FollowButton";
import { ChevronLeft, ChevronRight, Users } from "lucide-react";
import { useRef } from "react";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Link } from "react-router-dom";

export const PopularArtists = () => {
  const { popularArtists, fetchPopularArtists, isLoading } = useMusicStore();
  const {
    getArtistFollowerCount,
    getArtistLikeCount,
    artistLikeCounts,
    artistFollowerCounts,
  } = useEngagementStore();

  useEffect(() => {
    fetchPopularArtists();
  }, [fetchPopularArtists]);

  if (isLoading) {
    return (
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <Users className="h-6 w-6" />
            Popular Artists
          </h2>
        </div>
        <div className="flex gap-4 overflow-hidden">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="flex-shrink-0 w-48 animate-pulse">
              <div className="bg-white rounded-lg p-4 text-center">
                <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto mb-3"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!popularArtists.length) {
    return null;
  }

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          {/* <Users className="h-6 w-6" /> */}
          Popular Artists
        </h2>
      </div>

      <ScrollArea>
        <div
          // ref={scrollContainerRef}
          className="flex gap-2 overflow-x-auto scrollbar-hide pb-2"
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
        >
          {popularArtists.map((artist) => {
            const followerCount = getArtistFollowerCount(
              artist._id,
              artist.followerCount || 0
            );
            const likeCount = getArtistLikeCount(
              artist._id,
              artist.totalLikes || 0
            );

            return (
              <div key={artist._id} className="flex-shrink-0 w-24 bg-white rounded-lg text-center cursor-pointer group">
                <Link to={`/artist/${artist._id}`} key={artist._id}>
                  <Avatar className="w-20 h-20 mx-auto mb-3">
                    <AvatarImage
                      src={artist.imageUrl}
                      alt={artist.name}
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <AvatarFallback className="bg-gradient-to-br from-green-400 to-blue-600 text-white text-lg font-bold">
                      {artist.name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>

                  <h3 className="text-gray-900 font-semibold mb-3 group-hover:underline group-hover:text-primary">
                    {artist.name}
                  </h3>

                  {/* <p
                    className="text-sm truncate"
                    style={{
                      color: "#86868b",
                      fontSize: "14px",
                      fontWeight: "400",
                      lineHeight: "1.4",
                    }}
                  >
                    {artistFollowerCounts.get(artist._id) || followerCount}{" "}
                    Followers
                  </p> */}
                  {/* <p
                    className="text-sm truncate"
                    style={{
                      color: "#86868b",
                      fontSize: "14px",
                      fontWeight: "400",
                      lineHeight: "1.4",
                    }}
                  >
                    {artistLikeCounts.get(artist._id) || likeCount} Likes
                  </p> */}
                </Link>
              </div>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
};
