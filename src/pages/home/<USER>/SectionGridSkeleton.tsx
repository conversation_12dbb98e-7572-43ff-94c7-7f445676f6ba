import { Skeleton } from "@/components/ui/skeleton";

const SectionGridSkeleton = () => {
	return (
		<div className='mb-8'>
			<div className="flex items-center justify-between mb-6">
				<Skeleton className="h-8 w-48" style={{ backgroundColor: "#e8e8ea" }} />
				<Skeleton className="h-4 w-20" style={{ backgroundColor: "#e8e8ea" }} />
			</div>
			
			<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6'>
				{Array.from({ length: 6 }).map((_, i) => (
					<div key={i} className='animate-pulse'>
						<div className='mb-4'>
							<Skeleton 
								className='aspect-square rounded-xl' 
								style={{ backgroundColor: "#e8e8ea" }}
							/>
						</div>
						<div className='space-y-2'>
							<Skeleton className="h-4 w-full" style={{ backgroundColor: "#e8e8ea" }} />
							<Skeleton className="h-3 w-3/4" style={{ backgroundColor: "#e8e8ea" }} />
						</div>
					</div>
				))}
			</div>
		</div>
	);
};

export default SectionGridSkeleton;
