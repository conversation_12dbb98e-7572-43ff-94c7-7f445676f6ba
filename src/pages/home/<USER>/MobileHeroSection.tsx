import { useMusicStore } from "@/stores/useMusicStore";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, Heart, Music } from "lucide-react";
import { useEffect, useState } from "react";
import { LikeButton } from "@/components/ui/LikeButton";

const MobileHeroSection = () => {
  const { featuredSongs, isLoading, fetchArtists } = useMusicStore();
  const { setCurrentSong } = usePlayerStore();
  const [viewportWidth, setViewportWidth] = useState<number | null>(null);

  const heroSong = featuredSongs[0];

  useEffect(() => {
    fetchArtists();
    if (window !== undefined) {
      setViewportWidth(window.innerWidth - 10);
      // console.log(innerWidth)
    }
  }, [fetchArtists]);

  if (isLoading || !heroSong) return null;

  const handlePlay = () => {
    setCurrentSong(heroSong);
  };

  return (
    <div
      className={`mb-8 w-${viewportWidth} mt-5 p-2`}
      style={{ width: "clamp(280px, 100vw, 500px)" }}
    >
      <div
        className="relative rounded-xl overflow-hidden shadow-lg !mx-auto w-full"
        style={{
          // Responsive height using clamp() for dynamic scaling
          height: "clamp(280px, 40vh, 360px)",
          // Ensure proper aspect ratio on very wide screens
          maxWidth: "100%",
          // Responsive width with safe margins
          //   width: "clamp(280px, 80vw, 440px)",
          //   margin: "0 auto",
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        }}
      >
        {/* Background Image - Fully responsive */}
        <div className="absolute inset-0">
          <img
            src={heroSong.imageUrl}
            alt={heroSong.title}
            className="w-full h-full object-cover"
            style={{
              // Ensure image covers container properly on all screen sizes
              objectPosition: "center center",
            }}
          />
          {/* Enhanced gradient overlay for better text readability */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/85 via-black/30 to-black/10" />
        </div>

        {/* Content - Responsive padding and spacing */}
        <div
          className="relative z-10 h-full flex flex-col justify-between"
          style={{
            // Responsive padding using clamp()
            padding: "clamp(1rem, 4vw, 1.5rem)",
          }}
        >
          {/* Badge */}
          <div className="flex justify-between items-start mb-2">
            <Badge
              variant="secondary"
              className="bg-white/20 text-white border-0 backdrop-blur-sm text-xs font-medium"
              style={{
                // Responsive badge sizing
                padding: "0.25rem 0.5rem",
                fontSize: "clamp(0.7rem, 2.5vw, 0.75rem)",
              }}
            >
              <Music className="w-3 h-3 mr-1" />
              Featured
            </Badge>

            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/20 rounded-full transition-all duration-200 active:scale-95"
              style={{
                // Responsive button sizing
                width: "clamp(2rem, 8vw, 2.5rem)",
                height: "clamp(2rem, 8vw, 2.5rem)",
              }}
            >
              <LikeButton
                songId={heroSong._id}
                size="sm"
                variant="ghost"
                // className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 ml-2"
              />
            </Button>
          </div>

          {/* Song Info - Responsive typography */}
          <div className="text-center flex-1 flex flex-col justify-center">
            <h2
              className="font-bold text-white mb-2 line-clamp-2"
              style={{
                // Responsive title sizing
                fontSize: "clamp(1.25rem, 5vw, 1.75rem)",
                lineHeight: "1.2",
                fontFamily:
                  "SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif",
              }}
            >
              {heroSong.title}
            </h2>
            <p
              className="text-white/90 mb-4"
              style={{
                // Responsive artist text
                fontSize: "clamp(0.9rem, 3.5vw, 1.1rem)",
                lineHeight: "1.4",
              }}
            >
              {heroSong.artist}
            </p>

            {/* Play Button - Responsive sizing */}
            <Button
              onClick={handlePlay}
              className="bg-white text-black hover:bg-white/90 rounded-full font-semibold transition-all duration-200 hover:scale-105 active:scale-95 mx-auto"
              style={{
                // Responsive button sizing
                padding: "clamp(0.5rem, 3vw, 0.75rem) clamp(1rem, 6vw, 1.5rem)",
                fontSize: "clamp(0.8rem, 3vw, 0.9rem)",
                minHeight: "44px", // Minimum touch target size
                boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
              }}
            >
              <Play className="w-4 h-4 mr-2" />
              Play Now
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileHeroSection;
