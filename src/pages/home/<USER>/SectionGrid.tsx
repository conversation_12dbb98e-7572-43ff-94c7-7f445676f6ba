import { Song } from "@/types";
import SectionGridSkeleton from "./SectionGridSkeleton";
import { Button } from "@/components/ui/button";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { LikeButton } from "@/components/ui/LikeButton";
import { Play, Pause } from "lucide-react";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Link } from "react-router-dom";

type SectionGridProps = {
  title: string;
  songs: Song[];
  isLoading: boolean;
};

const SectionGrid = ({ songs, title, isLoading }: SectionGridProps) => {
  const { currentSong, isPlaying, setCurrentSong, togglePlay } =
    usePlayerStore();
  const { trackSongPlay } = useEngagementStore();

  if (isLoading) return <SectionGridSkeleton />;

  const handlePlay = (song: Song) => {
    if (currentSong?._id === song._id) {
      togglePlay();
    } else {
      setCurrentSong(song);
      // Track the play when a new song starts
      trackSongPlay(song._id);
    }
  };

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-6">
        <h2
          className="text-2xl font-semibold"
          style={{
            color: "#1d1d1f",
            fontWeight: "600",
            lineHeight: "1.3",
          }}
        >
          {title}
        </h2>
        <Button
          variant="link"
          className="text-sm hover:text-opacity-80 transition-opacity p-0 h-auto"
          style={{
            color: "#86868b",
            fontWeight: "500",
          }}
        >
          Show all
        </Button>
      </div>
      <ScrollArea className="h-full">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-6">
          {songs.map((song) => {
            const isCurrentSong = currentSong?._id === song._id;
            const isCurrentlyPlaying = isCurrentSong && isPlaying;

            return (
              <div
                key={song._id}
                className="group cursor-pointer transition-all duration-300 hover:scale-[1.02]"
                onClick={() => handlePlay(song)}
              >
                <div className="relative mb-4">
                  <div
                    className="aspect-square overflow-hidden transition-all duration-300"
                    style={{
                      backgroundColor: "#ffffff",
                      borderRadius: "12px",
                      // boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                    }}
                  >
                    <img
                      src={song.imageUrl}
                      alt={song.title}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />

                    {/* Play Button Overlay */}
                    <div
                      className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-300 ${
                        isCurrentlyPlaying
                          ? "opacity-100"
                          : "opacity-0 group-hover:opacity-100"
                      }`}
                    >
                      <Button
                        size="icon"
                        className="bg-white text-black hover:bg-white/90 w-14 h-14 rounded-full transition-all duration-200 hover:scale-110"
                        style={{
                          boxShadow: "0 8px 24px rgba(0,0,0,0.15)",
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePlay(song);
                        }}
                      >
                        {isCurrentlyPlaying ? (
                          <Pause className="w-6 h-6" />
                        ) : (
                          <Play className="w-6 h-6" />
                        )}
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <h3
                      className="font-medium  flex-1 "
                      style={{
                        color: "#1d1d1f",
                        fontSize: "16px",
                        fontWeight: "600",
                        lineHeight: "1.3",
                      }}
                    >
                      <Link
                        to={`/song/${song._id}`}
                        className="group-hover:text-primary group-hover:underline"
                      >
                        {song.title}
                      </Link>
                    </h3>
                    <LikeButton
                      songId={song._id}
                      size="sm"
                      variant="ghost"
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 ml-2"
                    />
                  </div>
                  <p
                    className="text-sm "
                    style={{
                      color: "#86868b",
                      fontSize: "14px",
                      fontWeight: "400",
                      lineHeight: "1.4",
                    }}
                  >
                    {song.artist}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
};

export default SectionGrid;
