import { Song } from "@/types";
import { Button } from "@/components/ui/button";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { LikeButton } from "@/components/ui/LikeButton";
import { Pause, Play } from "lucide-react";
import { Link } from "react-router-dom";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";

type TabletSectionGridProps = {
  title: string;
  songs: Song[];
  isLoading: boolean;
};

const TabletSectionGrid = ({
  songs,
  title,
  isLoading,
}: TabletSectionGridProps) => {
  const { currentSong, isPlaying, setCurrentSong, togglePlay } =
    usePlayerStore();
  const { trackSongPlay } = useEngagementStore();

  if (isLoading) {
    return (
      <div className="px-6 mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
          <Button
            variant="link"
            className="text-sm text-gray-600 hover:text-gray-900 p-0 h-auto"
          >
            Show all
          </Button>
        </div>
        <div className="flex flex-wrap gap-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="w-40 animate-pulse">
              <div className="aspect-square rounded-lg bg-gray-200 mb-3"></div>
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const handlePlay = (song: Song) => {
    if (currentSong?._id === song._id) {
      togglePlay();
    } else {
      setCurrentSong(song);
      // Track the play when a new song starts
      trackSongPlay(song._id);
    }
  };

  const handleShowAll = () => {
    // TODO: Navigate to full section page
    console.log(`Show all ${title} songs`);
  };

  return (
    <div className="px-6 mb-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
        <Button
          variant="link"
          className="text-sm text-gray-600 hover:text-gray-900 p-0 h-auto"
          onClick={handleShowAll}
        >
          Show all
        </Button>
      </div>

      {/* Flex-wrap Card Container */}
      <ScrollArea className="h-full">
        <div className="flex-wrap-container tablet-card-container content-constrained">
          {songs.map((song) => {
            const isCurrentSong = currentSong?._id === song._id;
            const isCurrentlyPlaying = isCurrentSong && isPlaying;

            return (
              <div
                key={song._id}
                className="responsive-card tablet-card-item cursor-pointer group tablet-smooth-transition tablet-hover-scale tablet-hover-shadow"
                onClick={() => handlePlay(song)}
              >
                  <div className="relative mb-3">
                    <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 shadow-sm transition-shadow duration-200">
                      <img
                        src={song.imageUrl}
                        alt={song.title}
                        className="w-full h-full object-cover"
                      />

                      {/* Play Button and Like Button Overlay */}
                      <div
                        className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-200 ${
                          isCurrentlyPlaying
                            ? "opacity-100"
                            : "opacity-0 group-hover:opacity-100"
                        }`}
                      >
                        <div className="flex items-center gap-2">
                          <Button
                            size="icon"
                            className="bg-white text-black hover:bg-white/90 w-12 h-12 rounded-full transition-all duration-200 hover:scale-110"
                            onClick={(e) => {
                              e.stopPropagation();
                              handlePlay(song);
                            }}
                          >
                            {isCurrentlyPlaying ? (
                              <Pause className="w-5 h-5" />
                            ) : (
                              <Play className="w-5 h-5" />
                            )}
                          </Button>
                          <LikeButton
                            songId={song._id}
                            variant="ghost"
                            size="sm"
                            className="bg-white/20 hover:bg-white/30 text-white border-none backdrop-blur-sm"
                            showCount={false}
                            count={song.likeCount || 0}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <h3
                      className="font-medium  flex-1 "
                    >
                      <Link
                        to={`/song/${song._id}`}
                        className="group-hover:text-primary group-hover:underline text-sm"
                      >
                        {song.title}
                      </Link>
                    </h3>
                    <p className="text-xs text-gray-600">
                      {song.artist}
                    </p>
                  </div>
              </div>
            );
          })}
        </div>
        <ScrollBar orientation="horizontal" className="mt-2" />
      </ScrollArea>
    </div>
  );
};

export default TabletSectionGrid;
