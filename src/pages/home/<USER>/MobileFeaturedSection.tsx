import { useMusicStore } from "@/stores/useMusicStore";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { LikeButton } from "@/components/ui/LikeButton";
import { But<PERSON> } from "@/components/ui/button";
import { Pause, Play } from "lucide-react";
import HorizontalScrollSection from "@/components/ui/HorizontalScrollSection";

const MobileFeaturedSection = () => {
  const { isLoading, featuredSongs, error } = useMusicStore();
  const { currentSong, isPlaying, setCurrentSong, togglePlay } = usePlayerStore();
  const { trackSongPlay } = useEngagementStore();

  if (isLoading) return <div className="px-4 mb-8">Loading...</div>;
  if (error) return <div className="px-4 mb-8 text-red-500">{error}</div>;

  const quickAccessSongs = featuredSongs.slice(0, 8);

  const handlePlay = (song: any) => {
    if (currentSong?._id === song._id) {
      togglePlay();
    } else {
      setCurrentSong(song);
      // Track the play when a new song starts
      trackSongPlay(song._id);
    }
  };

  const handleShowAll = () => {
    // TODO: Navigate to full Quick Access page
    console.log('Show all Quick Access songs');
  };

  return (
    // <HorizontalScrollSection
    //   title="Quick Access"
    //   onShowAll={handleShowAll}
    // >
    <div className="flex flex-wrap max-w-md justify-center gap-4 px-4 pb-2">
      {quickAccessSongs.map((song) => {
        const isCurrentSong = currentSong?._id === song._id;
        const isCurrentlyPlaying = isCurrentSong && isPlaying;
        
        return (
          <div
            key={song._id}
            className="flex-shrink-0 w-40 cursor-pointer group"
            onClick={() => handlePlay(song)}
          >
            <div className="relative mb-3">
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 shadow-sm">
                <img
                  src={song.imageUrl}
                  alt={song.title}
                  className="w-full h-full object-cover"
                />
                
                {/* Play Button and Like Button Overlay */}
                <div className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-200 ${
                  isCurrentlyPlaying ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
                }`}>
                  <div className="flex items-center gap-2">
                    <Button
                      size="icon"
                      className="bg-white text-black hover:bg-white/90 w-12 h-12 rounded-full"
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePlay(song);
                      }}
                    >
                      {isCurrentlyPlaying ? (
                        <Pause className="w-5 h-5" />
                      ) : (
                        <Play className="w-5 h-5" />
                      )}
                    </Button>
                    <LikeButton
                      songId={song._id}
                      variant="ghost"
                      size="sm"
                      className="bg-white/20 hover:bg-white/30 text-white border-none backdrop-blur-sm"
                      showCount={false}
                      count={song.likeCount || 0}
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-1">
              <h3 className="font-medium text-gray-900 truncate text-sm">
                {song.title}
              </h3>
              <p className="text-xs text-gray-600 truncate">
                {song.artist}
              </p>
            </div>
          </div>
        );
      })}
    {/* </HorizontalScrollSection> */}
      </div>
  );
};

export default MobileFeaturedSection;
