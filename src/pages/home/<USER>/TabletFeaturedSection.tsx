import { useMusicStore } from "@/stores/useMusicStore";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { LikeButton } from "@/components/ui/LikeButton";
import { But<PERSON> } from "@/components/ui/button";
import { Play, Pause } from "lucide-react";

const TabletFeaturedSection = () => {
  const { isLoading, featuredSongs, error } = useMusicStore();
  const { currentSong, isPlaying, setCurrentSong, togglePlay } = usePlayerStore();
  const { trackSongPlay } = useEngagementStore();

  if (isLoading) {
    return (
      <div className="px-6 mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Quick Access</h2>
          <Button
            variant="link"
            className="text-sm text-gray-600 hover:text-gray-900 p-0 h-auto"
          >
            Show all
          </Button>
        </div>
        <div className="flex flex-wrap gap-3">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="flex items-center bg-gray-100 rounded-lg overflow-hidden animate-pulse" style={{ width: "calc(50% - 6px)" }}>
              <div className="w-16 h-16 bg-gray-200"></div>
              <div className="flex-1 p-3">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) return <div className="px-6 mb-8 text-red-500">{error}</div>;

  // Take first 8 songs for quick access on tablet
  const quickAccessSongs = featuredSongs.slice(0, 8);

  const handlePlay = (song: any) => {
    if (currentSong?._id === song._id) {
      togglePlay();
    } else {
      setCurrentSong(song);
      // Track the play when a new song starts
      trackSongPlay(song._id);
    }
  };

  const handleShowAll = () => {
    // TODO: Navigate to full Quick Access page
    console.log('Show all Quick Access songs');
  };

  return (
    <div className="px-6 mb-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">
          Quick Access
        </h2>
        <Button
          variant="link"
          className="text-sm text-gray-600 hover:text-gray-900 p-0 h-auto"
          onClick={handleShowAll}
        >
          Show all
        </Button>
      </div>

      {/* Flex-wrap Card Container */}
      <div className="tablet-quick-access-grid content-constrained">
        {quickAccessSongs.map((song) => {
          const isCurrentSong = currentSong?._id === song._id;
          const isCurrentlyPlaying = isCurrentSong && isPlaying;
          
          return (
            <div
              key={song._id}
              className="tablet-quick-access-item group flex items-center overflow-hidden cursor-pointer tablet-smooth-transition tablet-hover-scale tablet-hover-shadow bg-white rounded-lg shadow-sm"
              onClick={() => handlePlay(song)}
            >
              {/* Thumbnail */}
              <div className="relative flex-shrink-0">
                <img
                  src={song.imageUrl}
                  alt={song.title}
                  className="w-16 h-16 object-cover"
                />
                
                {/* Play Button and Like Button Overlay */}
                <div className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-200 ${
                  isCurrentlyPlaying ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
                }`}>
                  <div className="flex items-center gap-1">
                    <Button
                      size="icon"
                      className="bg-white text-black hover:bg-white/90 w-8 h-8 rounded-full transition-all duration-200 hover:scale-110"
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePlay(song);
                      }}
                    >
                      {isCurrentlyPlaying ? (
                        <Pause className="w-3 h-3" />
                      ) : (
                        <Play className="w-3 h-3" />
                      )}
                    </Button>
                    <LikeButton
                      songId={song._id}
                      variant="ghost"
                      size="sm"
                      className="bg-white/20 hover:bg-white/30 text-white border-none backdrop-blur-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0 px-3 py-2">
                <h3 className="font-medium text-gray-900 truncate text-sm mb-1">
                  {song.title}
                </h3>
                <p className="text-xs text-gray-600 truncate">
                  {song.artist}
                </p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default TabletFeaturedSection;