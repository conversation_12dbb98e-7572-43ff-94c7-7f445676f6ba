import { useMusicStore } from "@/stores/useMusicStore";
import FeaturedGridSkeleton from "@/components/skeletons/FeaturedGridSkeleton";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { LikeButton } from "@/components/ui/LikeButton";
import { Button } from "@/components/ui/button";
import { Play, Pause } from "lucide-react";

const FeaturedSection = () => {
	const { isLoading, featuredSongs, error } = useMusicStore();
	const { currentSong, isPlaying, setCurrentSong, togglePlay } = usePlayerStore();
	const { trackSongPlay } = useEngagementStore();

	if (isLoading) return <FeaturedGridSkeleton />;
	if (error) return <p className='text-red-500 mb-4 text-lg'>{error}</p>;

	// Take first 6 songs for quick access
	const quickAccessSongs = featuredSongs.slice(0, 6);

	const handlePlay = (song: any) => {
		if (currentSong?._id === song._id) {
			togglePlay();
		} else {
			setCurrentSong(song);
			// Track the play when a new song starts
			trackSongPlay(song._id);
		}
	};

	return (
		<div className="mb-12">
			<h2 
				className="text-2xl font-semibold mb-6"
				style={{ 
					color: "#1d1d1f",
					// fontFamily: "SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif",
					fontWeight: "600",
					lineHeight: "1.3"
				}}
			>
				Quick Access
			</h2>
			
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				{quickAccessSongs.map((song) => {
					const isCurrentSong = currentSong?._id === song._id;
					const isCurrentlyPlaying = isCurrentSong && isPlaying;
					
					return (
						<div
							key={song._id}
							className="group flex items-center overflow-hidden cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
							style={{
								backgroundColor: "#ffffff",
								borderRadius: "12px",
								boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
							}}
							onClick={() => handlePlay(song)}
						>
							{/* Thumbnail */}
							<div className="relative flex-shrink-0">
								<img
									src={song.imageUrl}
									alt={song.title}
									className="w-20 h-20 object-cover"
									style={{ borderRadius: "8px" }}
								/>
								
								{/* Play Button and Like Button Overlay */}
								<div className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-300 ${
									isCurrentlyPlaying ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
								}`}>
									<div className="flex items-center gap-2">
										<Button
											size="icon"
											className="bg-white text-black hover:bg-white/90 w-10 h-10 rounded-full transition-all duration-200 hover:scale-110"
											style={{
												boxShadow: "0 8px 24px rgba(0,0,0,0.15)"
											}}
											onClick={(e) => {
												e.stopPropagation();
												handlePlay(song);
											}}
										>
											{isCurrentlyPlaying ? (
												<Pause className="w-4 h-4" />
											) : (
												<Play className="w-4 h-4" />
											)}
										</Button>
										<LikeButton
											songId={song._id}
											variant="ghost"
											size="sm"
											className="bg-white/20 hover:bg-white/30 text-white border-none backdrop-blur-sm"
											showCount={false}
											count={song.likeCount || 0}
										/>
									</div>
								</div>
							</div>

							{/* Content */}
							<div className="flex-1 min-w-0 px-4 py-3">
								<h3 
									className="font-medium truncate mb-1"
									style={{ 
										color: "#1d1d1f",
										fontSize: "16px",
										fontWeight: "600",
										lineHeight: "1.3"
									}}
								>
									{song.title}
								</h3>
								<p 
									className="text-sm truncate"
									style={{ 
										color: "#86868b",
										fontSize: "14px",
										fontWeight: "400",
										lineHeight: "1.4"
									}}
								>
									{song.artist}
								</p>
							</div>
						</div>
					);
				})}
			</div>
		</div>
	);
};

export default FeaturedSection;
