import { useState, useEffect } from "react";
import { usePlaylistStore } from "@/stores/usePlaylistStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { useMusicStore } from "@/stores/useMusicStore";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { ScrollArea } from "@/components/ui/scroll-area";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import LibraryHeader from "@/components/library/LibraryHeader";
import LibraryContent from "@/components/library/LibraryContent";
import { PlaylistSelectionModal } from "@/components/PlaylistSelectionModal";
import { Song, Playlist } from "@/types";

type LibraryCategory = "all" | "artists" | "playlists" | "albums";

const LibraryPage = () => {
  const [activeCategory, setActiveCategory] = useState<LibraryCategory>("all");
  const [selectedSongForPlaylist, setSelectedSongForPlaylist] = useState<
    string | null
  >(null);
  const [isPlaylistModalOpen, setIsPlaylistModalOpen] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const {
    userPlaylists,
    likedSongsPlaylist,
    fetchUserPlaylists,
    fetchLikedSongsPlaylist,
  } = usePlaylistStore();

  const {
    userLikedSongs,
    userFollowedArtists,
    fetchUserLikedSongs,
    fetchUserFollowedArtists,
  } = useEngagementStore();

  const { albums, fetchAlbums } = useMusicStore();

  const { currentSong, setCurrentSong, togglePlay, playAlbum } =
    usePlayerStore();

  useEffect(() => {
    // Fetch all library data when component mounts
    const fetchAllData = async () => {
      try {
        setIsInitialLoading(true);

        // Set a timeout to prevent infinite loading
        const timeoutId = setTimeout(() => {
          console.warn("Library data fetch is taking longer than expected");
          setIsInitialLoading(false);
        }, 10000); // 10 second timeout

        // Fetch all data concurrently with Promise.allSettled to handle individual failures
        const results = await Promise.allSettled([
          fetchUserPlaylists(),
          fetchLikedSongsPlaylist(),
          fetchUserLikedSongs(),
          fetchUserFollowedArtists(),
          fetchAlbums(),
        ]);

        // Clear the timeout since we completed
        clearTimeout(timeoutId);

        // Log any failures for debugging
        results.forEach((result, index) => {
          if (result.status === "rejected") {
            const functionNames = [
              "fetchUserPlaylists",
              "fetchLikedSongsPlaylist",
              "fetchUserLikedSongs",
              "fetchUserFollowedArtists",
              "fetchAlbums",
            ];
            console.error(`${functionNames[index]} failed:`, result.reason);
          }
        });
      } catch (error) {
        console.error("Error fetching library data:", error);
      } finally {
        setIsInitialLoading(false);
      }
    };

    fetchAllData();
  }, []); // Empty dependency array - only run once on mount

  const handlePlay = (song: Song) => {
    if (currentSong?._id === song._id) {
      togglePlay();
    } else {
      setCurrentSong(song);
    }
  };

  const handlePlayPlaylist = (playlist: Playlist) => {
    if (playlist.songs && playlist.songs.length > 0) {
      playAlbum(playlist.songs, 0);
    }
  };

  const handleAddToPlaylist = (songId: string) => {
    setSelectedSongForPlaylist(songId);
    setIsPlaylistModalOpen(true);
  };



  const categories = [
    {
      key: "all" as const,
      label: "All",
      count: userPlaylists.length + userFollowedArtists.length,
    },
    {
      key: "artists" as const,
      label: "Artists",
      count: userFollowedArtists.length,
    },
    {
      key: "playlists" as const,
      label: "Playlists",
      count: userPlaylists.length,
    },
    // { key: "albums" as const, label: "Albums", count: likedAlbums.length },
  ];

  // Show loading only during initial load, not for individual store operations
  if (isInitialLoading) {
    return <LoadingSpinner message="Loading your library..." />;
  }

  return (
    <div className="h-[90%] bg-white mt-5 md:mt-0">
      <ScrollArea className="h-full">
        <div className="p-6 space-y-6">
          {/* Header */}
          <LibraryHeader
            activeCategory={activeCategory}
            onCategoryChange={(category) => setActiveCategory(category as LibraryCategory)}
            categories={categories}
          />

          {/* Content */}
          <LibraryContent
            activeCategory={activeCategory}
            likedSongsPlaylist={likedSongsPlaylist}
            userPlaylists={userPlaylists}
            userFollowedArtists={userFollowedArtists}
            albums={albums}
            userLikedSongs={userLikedSongs}
            onPlayPlaylist={handlePlayPlaylist}
            onPlay={handlePlay}
            onAddToPlaylist={handleAddToPlaylist}
          />
        </div>
      </ScrollArea>

      {/* Playlist Selection Modal */}
      {selectedSongForPlaylist && (
        <PlaylistSelectionModal
          isOpen={isPlaylistModalOpen}
          onClose={() => {
            setIsPlaylistModalOpen(false);
            setSelectedSongForPlaylist(null);
          }}
          songId={selectedSongForPlaylist}
          songTitle={
            userLikedSongs.find((s) => s._id === selectedSongForPlaylist)?.title
          }
        />
      )}
    </div>
  );
};

export default LibraryPage;
