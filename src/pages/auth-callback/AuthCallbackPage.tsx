import { Card, CardContent } from "@/components/ui/card";
// import { useSession } from "@/lib/auth-client";
import { Loader } from "lucide-react";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const AuthCallbackPage = () => {
	// const { data: session, isPending } = useSession();
	const navigate = useNavigate();

	useEffect(() => {
		// Better Auth handles the callback automatically
		// Just redirect to home after a short delay
		const timer = setTimeout(() => {
			navigate("/");
		}, 2000);

		return () => clearTimeout(timer);
	}, [navigate]);

	return (
		<div className='h-screen w-full bg-white flex items-center justify-center'>
			<Card className='w-[90%] max-w-md bg-white border-zinc-300'>
				<CardContent className='flex flex-col items-center gap-4 pt-6'>
					<Loader className='size-6 text-emerald-500 animate-spin' />
					<h3 className='text-zinc-400 text-xl font-bold'>Logging you in</h3>
					<p className='text-zinc-400 text-sm'>Redirecting...</p>
				</CardContent>
			</Card>
		</div>
	);
};
export default AuthCallbackPage;
