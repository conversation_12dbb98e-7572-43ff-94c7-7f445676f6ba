import { Button } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	<PERSON><PERSON>Header,
	<PERSON><PERSON>Title,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useMusicStore } from "@/stores/useMusicStore";
import { ArtistCombobox } from "@/components/ArtistCombobox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Upload, Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import toast from "react-hot-toast";

interface BulkSongData {
	title: string;
	artistId: string;
	featuredArtists: string;
	album: string;
	duration: string;
	audioFile: File | null;
	imageFile: File | null;
}

const BulkUploadSongsDialog = () => {
	const { albums, artists, bulkUploadSongs } = useMusicStore();
	const [isOpen, setIsOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [songs, setSongs] = useState<BulkSongData[]>([{
		title: "",
		artistId: "",
		featuredArtists: "",
		album: "",
		duration: "0",
		audioFile: null,
		imageFile: null,
	}]);

	const addSong = () => {
		setSongs([...songs, {
			title: "",
			artistId: "",
			featuredArtists: "",
			album: "",
			duration: "0",
			audioFile: null,
			imageFile: null,
		}]);
	};

	const removeSong = (index: number) => {
		setSongs(songs.filter((_, i) => i !== index));
	};

	const updateSong = (index: number, field: keyof BulkSongData, value: any) => {
		setSongs(songs.map((song, i) => 
			i === index ? { ...song, [field]: value } : song
		));
	};

	const handleSubmit = async () => {
		setIsLoading(true);

		try {
			// Validate all songs
			for (let i = 0; i < songs.length; i++) {
				const song = songs[i];
				if (!song.audioFile || !song.imageFile) {
					return toast.error(`Song ${i + 1}: Please upload both audio and image files`);
				}
				if (!song.artistId) {
					return toast.error(`Song ${i + 1}: Please select an artist`);
				}
				if (!song.title.trim()) {
					return toast.error(`Song ${i + 1}: Please enter a title`);
				}
			}

			// Create FormData for each song
			const formDataArray = songs.map((song) => {
				const selectedArtist = artists.find(a => a._id === song.artistId);
				const formData = new FormData();
				
				formData.append("title", song.title);
				formData.append("artist", selectedArtist?.name || ""); 
				formData.append("artistId", song.artistId);
				formData.append("duration", song.duration);
				
				if (song.featuredArtists.trim()) {
					formData.append("featuredArtists", song.featuredArtists);
				}
				if (song.album && song.album !== "none") {
					formData.append("albumId", song.album);
				}

				formData.append("audioFile", song.audioFile!);
				formData.append("imageFile", song.imageFile!);

				return formData;
			});

			await bulkUploadSongs(formDataArray);

			// Reset form
			setSongs([{
				title: "",
				artistId: "",
				featuredArtists: "",
				album: "",
				duration: "0",
				audioFile: null,
				imageFile: null,
			}]);
			setIsOpen(false);
		} catch (error: any) {
			toast.error("Failed to bulk upload songs: " + error.message);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={setIsOpen}>
			<DialogTrigger asChild>
				<Button variant="outline" className="bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-600">
					<Upload className="mr-2 h-4 w-4" />
					Bulk Upload Songs
				</Button>
			</DialogTrigger>

			<DialogContent className="bg-zinc-900 border-zinc-700 max-w-4xl max-h-[80vh] overflow-auto">
				<DialogHeader>
					<DialogTitle className='text-white'>Bulk Upload Songs</DialogTitle>
					<DialogDescription>Upload multiple songs at once</DialogDescription>
				</DialogHeader>

				<div className="space-y-6 py-4">
					{songs.map((song, index) => (
						<div key={index} className="p-4 border border-zinc-700 rounded-lg space-y-4">
							<div className="flex justify-between items-center">
								<h3 className="text-lg font-medium">Song {index + 1}</h3>
								{songs.length > 1 && (
									<Button
										variant="ghost"
										size="sm"
										onClick={() => removeSong(index)}
										className="text-red-400 hover:text-red-300"
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								)}
							</div>

							<div className="grid grid-cols-2 gap-4">
								{/* Audio File */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Audio File</label>
									<Input
										type="file"
										accept="audio/*"
										onChange={(e) => updateSong(index, 'audioFile', e.target.files?.[0] || null)}
										className="bg-zinc-800 border-zinc-700"
									/>
								</div>

								{/* Image File */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Image File</label>
									<Input
										type="file"
										accept="image/*"
										onChange={(e) => updateSong(index, 'imageFile', e.target.files?.[0] || null)}
										className="bg-zinc-800 border-zinc-700"
									/>
								</div>

								{/* Title */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Title</label>
									<Input
										value={song.title}
										onChange={(e) => updateSong(index, 'title', e.target.value)}
										className="bg-zinc-800 border-zinc-700"
										placeholder="Enter song title"
									/>
								</div>

								{/* Artist */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Artist</label>
									<ArtistCombobox
										value={song.artistId}
										onChange={(value) => updateSong(index, 'artistId', value)}
										onAddArtist={() => {}}
										placeholder="Select artist..."
									/>
								</div>

								{/* Featured Artists */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Featured Artists (Optional)</label>
									<Input
										value={song.featuredArtists}
										onChange={(e) => updateSong(index, 'featuredArtists', e.target.value)}
										className="bg-zinc-800 border-zinc-700"
										placeholder="e.g., Artist 1, Artist 2"
									/>
								</div>

								{/* Duration */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Duration (seconds)</label>
									<Input
										type="number"
										min="0"
										value={song.duration}
										onChange={(e) => updateSong(index, 'duration', e.target.value || "0")}
										className="bg-zinc-800 border-zinc-700"
									/>
								</div>

								{/* Album */}
								<div className="space-y-2 col-span-2">
									<label className="text-sm font-medium">Album (Optional)</label>
									<Select
										value={song.album}
										onValueChange={(value) => updateSong(index, 'album', value)}
									>
										<SelectTrigger className="bg-zinc-800 border-zinc-700">
											<SelectValue placeholder="Select album" />
										</SelectTrigger>
										<SelectContent className="bg-zinc-800 border-zinc-700">
											<SelectItem value="none">No Album (Single)</SelectItem>
											{albums.map((album) => (
												<SelectItem key={album._id} value={album._id}>
													{album.title}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>
							</div>
						</div>
					))}

					<Button
						onClick={addSong}
						variant="outline"
						className="w-full border-dashed border-zinc-600 text-zinc-400 hover:text-zinc-300"
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Another Song
					</Button>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={() => setIsOpen(false)} disabled={isLoading}>
						Cancel
					</Button>
					<Button onClick={handleSubmit} disabled={isLoading}>
						{isLoading ? "Uploading..." : `Upload ${songs.length} Song${songs.length > 1 ? 's' : ''}`}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default BulkUploadSongsDialog;
