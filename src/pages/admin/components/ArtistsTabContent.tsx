import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users } from "lucide-react";
import ArtistsTable from "./ArtistsTable";
import AddArtistDialog from "./AddArtistDialog";
import BulkUploadArtistsDialog from "./BulkUploadArtistsDialog";

const ArtistsTabContent = () => {
	return (
		<Card className='bg-zinc-800/50 border-zinc-700/50'>
			<CardHeader>
				<div className='flex items-center justify-between'>
					<div>
						<CardTitle className='flex items-center gap-2'>
							<Users className='h-5 w-5 text-orange-500' />
							Artists Library
						</CardTitle>
						<CardDescription>Manage your artists</CardDescription>
					</div>
					<div className="flex gap-2">
						<BulkUploadArtistsDialog />
						<AddArtistDialog />
					</div>
				</div>
			</CardHeader>

			<CardContent>
				<ArtistsTable />
			</CardContent>
		</Card>
	);
};
export default ArtistsTabContent;
