import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	<PERSON><PERSON>Footer,
	<PERSON><PERSON>Header,
	<PERSON><PERSON>Title,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useMusicStore } from "@/stores/useMusicStore";
import { ArtistCombobox } from "@/components/ArtistCombobox";
import { AlbumCombobox } from "@/components/AlbumCombobox";
import AddArtistDialog from "./AddArtistDialog";
import AddAlbumDialog from "./AddAlbumDialog";
import { Plus, Upload, X } from "lucide-react";
import { useRef, useState } from "react";
import toast from "react-hot-toast";
import { getAudioDuration, formatDuration } from "@/utils/audioUtils";

interface NewSong {
	title: string;
	artistId: string;
	featuredArtists: string;
	album: string;
	duration: string;
	composer: string;
	producer: string;
	source: string;
	credits: Array<{ name: string; role: string }>;
}

const AddSongDialog = () => {
	const { artists, addSong } = useMusicStore();
	const [songDialogOpen, setSongDialogOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [addArtistDialogOpen, setAddArtistDialogOpen] = useState(false);
	const [addAlbumDialogOpen, setAddAlbumDialogOpen] = useState(false);
	const [detectedDuration, setDetectedDuration] = useState<number | null>(null);

	const [newSong, setNewSong] = useState<NewSong>({
		title: "",
		artistId: "",
		featuredArtists: "",
		album: "",
		duration: "0",
		composer: "",
		producer: "",
		source: "",
		credits: [],
	});

	const [currentCredit, setCurrentCredit] = useState({ name: "", role: "" });

	const [files, setFiles] = useState<{ audio: File | null; image: File | null }>({
		audio: null,
		image: null,
	});

	const audioInputRef = useRef<HTMLInputElement>(null);
	const imageInputRef = useRef<HTMLInputElement>(null);

	const handleAudioFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			setFiles((prev) => ({ ...prev, audio: file }));
			
			try {
				const duration = await getAudioDuration(file);
				setDetectedDuration(duration);
				setNewSong((prev) => ({ ...prev, duration: duration.toString() }));
				toast.success(`Duration detected: ${formatDuration(duration)}`);
			} catch (error) {
				console.error('Error detecting audio duration:', error);
				toast.error('Could not detect audio duration');
			}
		}
	};

	const handleAddCredit = () => {
		if (currentCredit.name.trim() && currentCredit.role.trim()) {
			setNewSong({
				...newSong,
				credits: [...newSong.credits, { ...currentCredit }]
			});
			setCurrentCredit({ name: "", role: "" });
		}
	};

	const handleRemoveCredit = (index: number) => {
		setNewSong({
			...newSong,
			credits: newSong.credits.filter((_, i) => i !== index)
		});
	};

	const handleSubmit = async () => {
		setIsLoading(true);

		try {
			if (!files.audio || !files.image) {
				return toast.error("Please upload both audio and image files");
			}

			if (!newSong.artistId) {
				return toast.error("Please select an artist");
			}

			const selectedArtist = artists.find(a => a._id === newSong.artistId);
			if (!selectedArtist) {
				return toast.error("Selected artist not found");
			}

			const formData = new FormData();

			formData.append("title", newSong.title);
			formData.append("artist", selectedArtist.name); // Still send artist name for backward compatibility
			formData.append("artistId", newSong.artistId);
			formData.append("duration", newSong.duration);
			formData.append("composer", newSong.composer);
			formData.append("producer", newSong.producer);
			formData.append("source", newSong.source);
			formData.append("credits", JSON.stringify(newSong.credits));
			if (newSong.featuredArtists.trim()) {
				formData.append("featuredArtists", newSong.featuredArtists);
			}
			if (newSong.album && newSong.album !== "none") {
				formData.append("albumId", newSong.album);
			}

			formData.append("audioFile", files.audio);
			formData.append("imageFile", files.image);

			await addSong(formData);

			setNewSong({
				title: "",
				artistId: "",
				featuredArtists: "",
				album: "",
				duration: "0",
				composer: "",
				producer: "",
				source: "",
				credits: [],
			});
			setCurrentCredit({ name: "", role: "" });

			setFiles({
				audio: null,
				image: null,
			});
			setDetectedDuration(null);
			setSongDialogOpen(false);
		} catch (error: any) {
			toast.error("Failed to add song: " + error.message);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<>
		<Dialog open={songDialogOpen} onOpenChange={setSongDialogOpen}>
			<DialogTrigger asChild>
				<Button className='bg-emerald-500 hover:bg-emerald-600 text-black'>
					<Plus className='mr-2 h-4 w-4' />
					Add Song
				</Button>
			</DialogTrigger>

			<DialogContent className='bg-zinc-900 border-zinc-700 max-h-[80vh] overflow-auto'>
				<DialogHeader>
					<DialogTitle className='text-white'>Add New Song</DialogTitle>
					<DialogDescription>Add a new song to your music library</DialogDescription>
				</DialogHeader>

				<div className='space-y-4 py-4'>
					<input
						type='file'
						accept='audio/*'
						ref={audioInputRef}
						hidden
						onChange={handleAudioFileChange}
					/>

					<input
						type='file'
						ref={imageInputRef}
						className='hidden'
						accept='image/*'
						onChange={(e) => setFiles((prev) => ({ ...prev, image: e.target.files![0] }))}
					/>

					{/* image upload area */}
					<div
						className='flex items-center justify-center p-6 border-2 border-dashed border-zinc-700 rounded-lg cursor-pointer'
						onClick={() => imageInputRef.current?.click()}
					>
						<div className='text-center'>
							{files.image ? (
								<div className='space-y-2'>
									<div className='text-sm text-emerald-500'>Image selected:</div>
									<div className='text-xs text-zinc-400'>{files.image.name.slice(0, 20)}</div>
								</div>
							) : (
								<>
									<div className='p-3 bg-zinc-800 rounded-full inline-block mb-2'>
										<Upload className='h-6 w-6 text-zinc-400' />
									</div>
									<div className='text-sm text-zinc-400 mb-2'>Upload artwork</div>
									<Button variant='outline' size='sm' className='text-xs'>
										Choose File
									</Button>
								</>
							)}
						</div>
					</div>

					{/* Audio upload */}
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Audio File</label>
						<div className='flex items-center gap-2'>
							<Button variant='outline' onClick={() => audioInputRef.current?.click()} className='w-full'>
								{files.audio ? files.audio.name.slice(0, 20) : "Choose Audio File"}
							</Button>
						</div>
					</div>

					{/* other fields */}
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Title</label>
						<Input
							value={newSong.title}
							onChange={(e) => setNewSong({ ...newSong, title: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
						/>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Artist</label>
						<ArtistCombobox
							value={newSong.artistId}
							onChange={(value) => setNewSong({ ...newSong, artistId: value })}
							onAddArtist={() => setAddArtistDialogOpen(true)}
							placeholder="Select artist..."
						/>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Featured Artists (Optional)</label>
						<Input
							value={newSong.featuredArtists}
							onChange={(e) => setNewSong({ ...newSong, featuredArtists: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder="e.g., Artist 1, Artist 2"
						/>
						<p className='text-xs text-zinc-500'>Separate multiple artists with commas</p>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Duration (seconds)</label>
						<Input
							type='number'
							min='0'
							value={newSong.duration}
							onChange={(e) => setNewSong({ ...newSong, duration: e.target.value || "0" })}
							className='bg-zinc-800 border-zinc-700'
							placeholder={detectedDuration ? `Auto-detected: ${detectedDuration}s` : "Enter duration"}
						/>
						{detectedDuration && (
							<p className='text-xs text-emerald-400'>
								Auto-detected: {formatDuration(detectedDuration)}
							</p>
						)}
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Composer</label>
						<Input
							value={newSong.composer}
							onChange={(e) => setNewSong({ ...newSong, composer: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder="Who composed the song?"
						/>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Producer</label>
						<Input
							value={newSong.producer}
							onChange={(e) => setNewSong({ ...newSong, producer: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder="Who produced the song?"
						/>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Source</label>
						<Input
							value={newSong.source}
							onChange={(e) => setNewSong({ ...newSong, source: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder="Source of the song (e.g., album, single, etc.)"
						/>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Credits</label>
						<div className='grid grid-cols-2 gap-2'>
							<Input
								value={currentCredit.name}
								onChange={(e) => setCurrentCredit({ ...currentCredit, name: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
								placeholder="Person's name"
							/>
							<div className='flex gap-2'>
								<Input
									value={currentCredit.role}
									onChange={(e) => setCurrentCredit({ ...currentCredit, role: e.target.value })}
									className='bg-zinc-800 border-zinc-700'
									placeholder="Their role"
								/>
								<Button
									type='button'
									variant='outline'
									onClick={handleAddCredit}
									disabled={!currentCredit.name.trim() || !currentCredit.role.trim()}
								>
									<Plus className='h-4 w-4' />
								</Button>
							</div>
						</div>
						{newSong.credits.length > 0 && (
							<div className='flex flex-wrap gap-2 mt-2'>
								{newSong.credits.map((credit, index) => (
									<Badge key={index} variant="secondary" className='flex items-center gap-1'>
										{credit.name} ({credit.role})
										<X
											className='h-3 w-3 cursor-pointer hover:text-red-500'
											onClick={() => handleRemoveCredit(index)}
										/>
									</Badge>
								))}
							</div>
						)}
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Album (Optional)</label>
						<AlbumCombobox
							value={newSong.album}
							onChange={(value) => setNewSong({ ...newSong, album: value })}
							onAddAlbum={() => setAddAlbumDialogOpen(true)}
							placeholder="Select album..."
						/>
					</div>
				</div>

				<DialogFooter>
					<Button variant='outline' onClick={() => setSongDialogOpen(false)} disabled={isLoading}>
						Cancel
					</Button>
					<Button onClick={handleSubmit} disabled={isLoading}>
						{isLoading ? "Uploading..." : "Add Song"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
		
		{/* Embedded Add Artist Dialog */}
		<Dialog open={addArtistDialogOpen} onOpenChange={setAddArtistDialogOpen}>
			<DialogContent className='bg-zinc-900 border-zinc-700'>
				<AddArtistDialog 
					isOpen={addArtistDialogOpen} 
					onClose={() => setAddArtistDialogOpen(false)}
					embedded={true}
				/>
			</DialogContent>
		</Dialog>

		{/* Embedded Add Album Dialog */}
		<Dialog open={addAlbumDialogOpen} onOpenChange={setAddAlbumDialogOpen}>
			<DialogContent className='bg-zinc-900 border-zinc-700'>
				<AddAlbumDialog 
					isOpen={addAlbumDialogOpen} 
					onClose={() => setAddAlbumDialogOpen(false)}
					embedded={true}
				/>
			</DialogContent>
		</Dialog>
		</>
	);
};
export default AddSongDialog;
