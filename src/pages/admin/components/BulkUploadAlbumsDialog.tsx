import { Button } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	<PERSON><PERSON>Header,
	<PERSON><PERSON>Title,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useMusicStore } from "@/stores/useMusicStore";
import { ArtistCombobox } from "@/components/ArtistCombobox";
import { Upload, Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import toast from "react-hot-toast";

interface BulkAlbumData {
	title: string;
	artistId: string;
	releaseYear: number;
	genre: string;
	imageFile: File | null;
}

const BulkUploadAlbumsDialog = () => {
	const { artists, bulkUploadAlbums } = useMusicStore();
	const [isOpen, setIsOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [albums, setAlbums] = useState<BulkAlbumData[]>([{
		title: "",
		artistId: "",
		releaseYear: new Date().getFullYear(),
		genre: "",
		imageFile: null,
	}]);

	const addAlbum = () => {
		setAlbums([...albums, {
			title: "",
			artistId: "",
			releaseYear: new Date().getFullYear(),
			genre: "",
			imageFile: null,
		}]);
	};

	const removeAlbum = (index: number) => {
		setAlbums(albums.filter((_, i) => i !== index));
	};

	const updateAlbum = (index: number, field: keyof BulkAlbumData, value: any) => {
		setAlbums(albums.map((album, i) => 
			i === index ? { ...album, [field]: value } : album
		));
	};

	const handleSubmit = async () => {
		setIsLoading(true);

		try {
			// Validate all albums
			for (let i = 0; i < albums.length; i++) {
				const album = albums[i];
				if (!album.imageFile) {
					return toast.error(`Album ${i + 1}: Please upload an image file`);
				}
				if (!album.artistId) {
					return toast.error(`Album ${i + 1}: Please select an artist`);
				}
				if (!album.title.trim()) {
					return toast.error(`Album ${i + 1}: Please enter a title`);
				}
				if (!album.genre.trim()) {
					return toast.error(`Album ${i + 1}: Please enter a genre`);
				}
			}

			// Create FormData for each album
			const formDataArray = albums.map((album) => {
				const selectedArtist = artists.find(a => a._id === album.artistId);
				const formData = new FormData();
				
				formData.append("title", album.title);
				formData.append("artist", selectedArtist?.name || ""); 
				formData.append("artistId", album.artistId);
				formData.append("releaseYear", album.releaseYear.toString());
				formData.append("genre", album.genre);
				formData.append("imageFile", album.imageFile!);

				return formData;
			});

			await bulkUploadAlbums(formDataArray);

			// Reset form
			setAlbums([{
				title: "",
				artistId: "",
				releaseYear: new Date().getFullYear(),
				genre: "",
				imageFile: null,
			}]);
			setIsOpen(false);
		} catch (error: any) {
			toast.error("Failed to bulk upload albums: " + error.message);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={setIsOpen}>
			<DialogTrigger asChild>
				<Button variant="outline" className="bg-violet-600 hover:bg-violet-700 text-white border-violet-600">
					<Upload className="mr-2 h-4 w-4" />
					Bulk Upload Albums
				</Button>
			</DialogTrigger>

			<DialogContent className="bg-zinc-900 border-zinc-700 max-w-4xl max-h-[80vh] overflow-auto">
				<DialogHeader>
					<DialogTitle className='text-white'>Bulk Upload Albums</DialogTitle>
					<DialogDescription>Upload multiple albums at once</DialogDescription>
				</DialogHeader>

				<div className="space-y-6 py-4">
					{albums.map((album, index) => (
						<div key={index} className="p-4 border border-zinc-700 rounded-lg space-y-4">
							<div className="flex justify-between items-center">
								<h3 className="text-lg font-medium">Album {index + 1}</h3>
								{albums.length > 1 && (
									<Button
										variant="ghost"
										size="sm"
										onClick={() => removeAlbum(index)}
										className="text-red-400 hover:text-red-300"
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								)}
							</div>

							<div className="grid grid-cols-2 gap-4">
								{/* Image File */}
								<div className="space-y-2 col-span-2">
									<label className="text-sm font-medium">Album Artwork</label>
									<Input
										type="file"
										accept="image/*"
										onChange={(e) => updateAlbum(index, 'imageFile', e.target.files?.[0] || null)}
										className="bg-zinc-800 border-zinc-700"
									/>
								</div>

								{/* Title */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Album Title</label>
									<Input
										value={album.title}
										onChange={(e) => updateAlbum(index, 'title', e.target.value)}
										className="bg-zinc-800 border-zinc-700"
										placeholder="Enter album title"
									/>
								</div>

								{/* Artist */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Artist</label>
									<ArtistCombobox
										value={album.artistId}
										onChange={(value) => updateAlbum(index, 'artistId', value)}
										onAddArtist={() => {}}
										placeholder="Select artist..."
									/>
								</div>

								{/* Genre */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Genre</label>
									<Input
										value={album.genre}
										onChange={(e) => updateAlbum(index, 'genre', e.target.value)}
										className="bg-zinc-800 border-zinc-700"
										placeholder="Enter genre (e.g., Pop, Rock, Hip-Hop)"
									/>
								</div>

								{/* Release Year */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Release Year</label>
									<Input
										type="number"
										value={album.releaseYear}
										onChange={(e) => updateAlbum(index, 'releaseYear', parseInt(e.target.value) || new Date().getFullYear())}
										className="bg-zinc-800 border-zinc-700"
										placeholder="Enter release year"
										min={1900}
										max={new Date().getFullYear()}
									/>
								</div>
							</div>
						</div>
					))}

					<Button
						onClick={addAlbum}
						variant="outline"
						className="w-full border-dashed border-zinc-600 text-zinc-400 hover:text-zinc-300"
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Another Album
					</Button>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={() => setIsOpen(false)} disabled={isLoading}>
						Cancel
					</Button>
					<Button onClick={handleSubmit} disabled={isLoading}>
						{isLoading ? "Uploading..." : `Upload ${albums.length} Album${albums.length > 1 ? 's' : ''}`}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default BulkUploadAlbumsDialog;
