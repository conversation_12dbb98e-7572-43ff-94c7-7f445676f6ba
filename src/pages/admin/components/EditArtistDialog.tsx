import { But<PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useMusicStore } from "@/stores/useMusicStore";
import { Upload, Plus, X } from "lucide-react";
import { useRef, useState, useEffect } from "react";
import toast from "react-hot-toast";
import { Artist } from "@/types";

interface EditArtistDialogProps {
	artist: Artist;
	isOpen: boolean;
	onClose: () => void;
}

const EditArtistDialog = ({ artist, isOpen, onClose }: EditArtistDialogProps) => {
	const { editArtist } = useMusicStore();
	const [isLoading, setIsLoading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const [editedArtist, setEditedArtist] = useState({
		name: "",
		bgColor: "#1f2937",
		bio: "",
		monthlyListeners: 0,
		genres: [] as string[],
		socialLinks: {
			spotify: "",
			instagram: "",
			twitter: "",
			website: "",
		},
	});

	const [currentGenre, setCurrentGenre] = useState("");
	const [imageFile, setImageFile] = useState<File | null>(null);

	// Initialize form with artist data
	useEffect(() => {
		if (artist) {
			setEditedArtist({
				name: artist.name,
				bgColor: artist.bgColor || "#1f2937",
				bio: artist.about || "",
				monthlyListeners: artist.monthlyListeners || 0,
				genres: artist.genres || [],
				socialLinks: {
					spotify: artist.socialLinks?.spotify || "",
					instagram: artist.socialLinks?.instagram || "",
					twitter: artist.socialLinks?.twitter || "",
					website: artist.socialLinks?.website || "",
				},
			});
		}
	}, [artist]);

	const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			setImageFile(file);
		}
	};

	const handleAddGenre = () => {
		if (currentGenre.trim() && !editedArtist.genres.includes(currentGenre.trim())) {
			setEditedArtist({
				...editedArtist,
				genres: [...editedArtist.genres, currentGenre.trim()]
			});
			setCurrentGenre("");
		}
	};

	const handleRemoveGenre = (genre: string) => {
		setEditedArtist({
			...editedArtist,
			genres: editedArtist.genres.filter(g => g !== genre)
		});
	};

	const handleGenreKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			e.preventDefault();
			handleAddGenre();
		}
	};

	const handleSubmit = async () => {
		setIsLoading(true);

		try {
			const formData = new FormData();
			formData.append("name", editedArtist.name);
			formData.append("bgColor", editedArtist.bgColor);
			formData.append("about", editedArtist.bio);
			formData.append("monthlyListeners", editedArtist.monthlyListeners.toString());
			formData.append("genres", JSON.stringify(editedArtist.genres));
			formData.append("socialLinks", JSON.stringify(editedArtist.socialLinks));
			
			// Only append image if it was changed
			if (imageFile) {
				formData.append("imageFile", imageFile);
			}

			await editArtist(artist._id, formData);
			onClose();
		} catch (error: any) {
			toast.error("Failed to update artist: " + error.message);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className='bg-zinc-900 border-zinc-700 max-h-[80vh] overflow-auto'>
				<DialogHeader>
					<DialogTitle className='text-white'>Edit Artist</DialogTitle>
					<DialogDescription className='text-gray-400'>Update the artist details</DialogDescription>
				</DialogHeader>

				<div className='space-y-4 py-4'>
					<input
						type='file'
						ref={fileInputRef}
						onChange={handleImageSelect}
						accept='image/*'
						className='hidden'
					/>

					{/* Current image preview */}
					<div className='space-y-2'>
						<label className='text-sm font-medium text-white'>Current Image</label>
						<div className='flex items-center gap-4'>
							<img 
								src={artist.imageUrl || '/placeholder-artist.jpg'} 
								alt={artist.name} 
								className='w-20 h-20 rounded-full object-cover' 
							/>
							<Button
								variant='outline'
								onClick={() => fileInputRef.current?.click()}
								className='flex items-center gap-2'
							>
								<Upload className='h-4 w-4' />
								{imageFile ? 'Change Image' : 'Update Image'}
							</Button>
						</div>
						{imageFile && (
							<div className='text-sm text-emerald-500'>
								New image selected: {imageFile.name}
							</div>
						)}
					</div>

					{/* Artist Name */}
					<div className='space-y-2'>
						<label className='text-sm font-medium text-white'>Artist Name</label>
						<Input
							value={editedArtist.name}
							onChange={(e) => setEditedArtist({ ...editedArtist, name: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder="Enter artist name"
						/>
					</div>

					{/* Background Color */}
					<div className='space-y-2'>
						<label className='text-sm font-medium text-white'>Background Color</label>
						<div className='flex gap-2'>
							<Input
								type='color'
								value={editedArtist.bgColor}
								onChange={(e) => setEditedArtist({ ...editedArtist, bgColor: e.target.value })}
								className='w-20 h-10 bg-zinc-800 border-zinc-700 cursor-pointer'
							/>
							<Input
								type='text'
								value={editedArtist.bgColor}
								onChange={(e) => setEditedArtist({ ...editedArtist, bgColor: e.target.value })}
								className='flex-1 bg-zinc-800 border-zinc-700'
								placeholder="#1f2937"
							/>
						</div>
					</div>

					{/* Color Preview */}
					<div className='space-y-2'>
						<label className='text-sm font-medium text-white'>Color Preview</label>
						<div className='flex items-center gap-2'>
							<div 
								className='w-12 h-12 rounded-full border border-zinc-600'
								style={{ backgroundColor: editedArtist.bgColor }}
							></div>
							<span className='text-sm text-zinc-400'>{editedArtist.bgColor}</span>
						</div>
					</div>

					{/* Bio */}
					<div className='space-y-2'>
						<label className='text-sm font-medium text-white'>Bio/About</label>
						<Textarea
							value={editedArtist.bio}
							onChange={(e) => setEditedArtist({ ...editedArtist, bio: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Tell us about the artist...'
							rows={3}
						/>
					</div>

					{/* Monthly Listeners */}
					<div className='space-y-2'>
						<label className='text-sm font-medium text-white'>Monthly Listeners</label>
						<Input
							type='number'
							min='0'
							value={editedArtist.monthlyListeners}
							onChange={(e) => setEditedArtist({ ...editedArtist, monthlyListeners: parseInt(e.target.value) || 0 })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Number of monthly listeners'
						/>
					</div>

					{/* Genres */}
					<div className='space-y-2'>
						<label className='text-sm font-medium text-white'>Genres</label>
						<div className='flex gap-2'>
							<Input
								value={currentGenre}
								onChange={(e) => setCurrentGenre(e.target.value)}
								onKeyPress={handleGenreKeyPress}
								className='bg-zinc-800 border-zinc-700'
								placeholder='Add a genre'
							/>
							<Button
								type='button'
								variant='outline'
								onClick={handleAddGenre}
								disabled={!currentGenre.trim() || editedArtist.genres.includes(currentGenre.trim())}
							>
								<Plus className='h-4 w-4' />
							</Button>
						</div>
						{editedArtist.genres.length > 0 && (
							<div className='flex flex-wrap gap-2 mt-2'>
								{editedArtist.genres.map((genre) => (
									<Badge key={genre} variant="secondary" className='flex items-center gap-1'>
										{genre}
										<X
											className='h-3 w-3 cursor-pointer hover:text-red-500'
											onClick={() => handleRemoveGenre(genre)}
										/>
									</Badge>
								))}
							</div>
						)}
					</div>

					{/* Social Links */}
					<div className='space-y-3'>
						<label className='text-sm font-medium text-white'>Social Links</label>
						<div className='grid grid-cols-2 gap-3'>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Spotify</label>
								<Input
									value={editedArtist.socialLinks.spotify}
									onChange={(e) => setEditedArtist({ 
										...editedArtist, 
										socialLinks: { ...editedArtist.socialLinks, spotify: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Spotify URL'
								/>
							</div>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Instagram</label>
								<Input
									value={editedArtist.socialLinks.instagram}
									onChange={(e) => setEditedArtist({ 
										...editedArtist, 
										socialLinks: { ...editedArtist.socialLinks, instagram: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Instagram URL'
								/>
							</div>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Twitter</label>
								<Input
									value={editedArtist.socialLinks.twitter}
									onChange={(e) => setEditedArtist({ 
										...editedArtist, 
										socialLinks: { ...editedArtist.socialLinks, twitter: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Twitter URL'
								/>
							</div>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Website</label>
								<Input
									value={editedArtist.socialLinks.website}
									onChange={(e) => setEditedArtist({ 
										...editedArtist, 
										socialLinks: { ...editedArtist.socialLinks, website: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Website URL'
								/>
							</div>
						</div>
					</div>
				</div>

				<DialogFooter>
					<Button variant='outline' onClick={onClose} disabled={isLoading}>
						Cancel
					</Button>
					<Button onClick={handleSubmit} disabled={isLoading}>
						{isLoading ? "Updating..." : "Update Artist"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default EditArtistDialog;
