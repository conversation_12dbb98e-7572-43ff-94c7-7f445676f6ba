import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useMusicStore } from "@/stores/useMusicStore";
import { Calendar, Edit, Trash2 } from "lucide-react";
import { useState } from "react";
import EditArtistDialog from "./EditArtistDialog";
import { Artist } from "@/types";

const ArtistsTable = () => {
  const { artists, deleteArtist } = useMusicStore();
  const [editingArtist, setEditingArtist] = useState<Artist | null>(null);

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow className="hover:bg-zinc-800/50">
            <TableHead className="w-[50px]"></TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Background Color</TableHead>
            <TableHead>Created Date</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {artists.map((artist) => (
            <TableRow key={artist._id} className={`hover:bg-zinc-800/50 ${artist._id.startsWith('temp-') ? 'opacity-60' : ''}`}>
              <TableCell>
                <Avatar>
                  <AvatarImage src={artist.imageUrl} />
                  <AvatarFallback>A</AvatarFallback>
                </Avatar>
              </TableCell>
              <TableCell className="font-medium">
                {artist.name}
                {artist._id.startsWith('temp-') && (
                  <span className="text-xs text-zinc-500 ml-2">(Uploading...)</span>
                )}
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <div
                    className="w-6 h-6 rounded-full border border-zinc-600"
                    style={{ backgroundColor: artist.bgColor || "#1f2937" }}
                  ></div>
                  <span className="text-sm text-zinc-400">
                    {artist.bgColor || "#1f2937"}
                  </span>
                </div>
              </TableCell>
              <TableCell>
                <span className="inline-flex items-center gap-1 text-zinc-400">
                  <Calendar className="h-4 w-4" />
                  {artist.createdAt.split("T")[0]}
                </span>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex gap-2 justify-end">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-blue-400 hover:text-blue-300 hover:bg-blue-400/10"
                    onClick={() => setEditingArtist(artist)}
                    disabled={artist._id.startsWith('temp-')}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteArtist(artist._id)}
                    className="text-red-400 hover:text-red-300 hover:bg-red-400/10"
                    disabled={artist._id.startsWith('temp-')}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Edit Artist Dialog */}
      {editingArtist && (
        <EditArtistDialog
          artist={editingArtist}
          isOpen={!!editingArtist}
          onClose={() => setEditingArtist(null)}
        />
      )}
    </>
  );
};
export default ArtistsTable;
