import { But<PERSON> } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useMusicStore } from "@/stores/useMusicStore";
import { ArtistCombobox } from "@/components/ArtistCombobox";
import AddArtistDialog from "./AddArtistDialog";
import { Upload } from "lucide-react";
import { useRef, useState, useEffect } from "react";
import toast from "react-hot-toast";
import { Album } from "@/types";

interface EditAlbumDialogProps {
	album: Album;
	isOpen: boolean;
	onClose: () => void;
}

const EditAlbumDialog = ({ album, isOpen, onClose }: EditAlbumDialogProps) => {
	const { artists, editAlbum } = useMusicStore();
	const [addArtistDialogOpen, setAddArtistDialogOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const [editedAlbum, setEditedAlbum] = useState({
		title: "",
		artistId: "",
		releaseYear: new Date().getFullYear(),
		genre: "",
	});

	const [imageFile, setImageFile] = useState<File | null>(null);

	// Initialize form with album data
	useEffect(() => {
		if (album) {
			setEditedAlbum({
				title: album.title,
				artistId: album.artistId || "",
				releaseYear: album.releaseYear,
				genre: album.genre,
			});
		}
	}, [album]);

	const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			setImageFile(file);
		}
	};

	const handleSubmit = async () => {
		setIsLoading(true);

		try {
			if (!editedAlbum.artistId) {
				return toast.error("Please select an artist");
			}

			const selectedArtist = artists.find(a => a._id === editedAlbum.artistId);
			if (!selectedArtist) {
				return toast.error("Selected artist not found");
			}

			const formData = new FormData();
			formData.append("title", editedAlbum.title);
			formData.append("artist", selectedArtist.name);
			formData.append("artistId", editedAlbum.artistId);
			formData.append("releaseYear", editedAlbum.releaseYear.toString());
			formData.append("genre", editedAlbum.genre);
			
			// Only append image if it was changed
			if (imageFile) {
				formData.append("imageFile", imageFile);
			}

			await editAlbum(album._id, formData);
			onClose();
		} catch (error: any) {
			toast.error("Failed to update album: " + error.message);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<>
			<Dialog open={isOpen} onOpenChange={onClose}>
				<DialogContent className='bg-zinc-900 border-zinc-700 max-h-[80vh] overflow-auto'>
					<DialogHeader>
						<DialogTitle className='text-white'>Edit Album</DialogTitle>
						<DialogDescription className='text-gray-400'>Update the album details</DialogDescription>
					</DialogHeader>

					<div className='space-y-4 py-4'>
						<input
							type='file'
							ref={fileInputRef}
							className='hidden'
							accept='image/*'
							onChange={handleImageSelect}
						/>

						{/* Current image preview */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Current Image</label>
							<div className='flex items-center gap-4'>
								<img 
									src={album.imageUrl || '/placeholder-album.jpg'} 
									alt={album.title} 
									className='w-20 h-20 rounded object-cover' 
								/>
								<Button
									variant='outline'
									onClick={() => fileInputRef.current?.click()}
									className='flex items-center gap-2'
								>
									<Upload className='h-4 w-4' />
									{imageFile ? 'Change Image' : 'Update Image'}
								</Button>
							</div>
							{imageFile && (
								<div className='text-sm text-emerald-500'>
									New image selected: {imageFile.name}
								</div>
							)}
						</div>

						{/* Title */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Title</label>
							<Input
								value={editedAlbum.title}
								onChange={(e) => setEditedAlbum({ ...editedAlbum, title: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
							/>
						</div>

						{/* Artist */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Artist</label>
							<ArtistCombobox
								value={editedAlbum.artistId}
								onChange={(value) => setEditedAlbum({ ...editedAlbum, artistId: value })}
								onAddArtist={() => setAddArtistDialogOpen(true)}
								placeholder="Select artist..."
							/>
						</div>

						{/* Release Year */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Release Year</label>
							<Input
								type='number'
								min='1900'
								max={new Date().getFullYear() + 1}
								value={editedAlbum.releaseYear}
								onChange={(e) => setEditedAlbum({ ...editedAlbum, releaseYear: parseInt(e.target.value) || new Date().getFullYear() })}
								className='bg-zinc-800 border-zinc-700'
							/>
						</div>

						{/* Genre */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Genre</label>
							<Input
								value={editedAlbum.genre}
								onChange={(e) => setEditedAlbum({ ...editedAlbum, genre: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
								placeholder="e.g., Pop, Rock, Hip-Hop"
							/>
						</div>
					</div>

					<DialogFooter>
						<Button variant='outline' onClick={onClose} disabled={isLoading}>
							Cancel
						</Button>
						<Button onClick={handleSubmit} disabled={isLoading}>
							{isLoading ? "Updating..." : "Update Album"}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
			
			{/* Embedded Add Artist Dialog */}
			<Dialog open={addArtistDialogOpen} onOpenChange={setAddArtistDialogOpen}>
				<DialogContent className='bg-zinc-900 border-zinc-700'>
					<AddArtistDialog 
						isOpen={addArtistDialogOpen} 
						onClose={() => setAddArtistDialogOpen(false)}
						embedded={true}
					/>
				</DialogContent>
			</Dialog>
		</>
	);
};

export default EditAlbumDialog;
