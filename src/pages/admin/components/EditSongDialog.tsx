import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	Di<PERSON>Header,
	DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useMusicStore } from "@/stores/useMusicStore";
import { ArtistCombobox } from "@/components/ArtistCombobox";
import { AlbumCombobox } from "@/components/AlbumCombobox";
import AddArtistDialog from "./AddArtistDialog";
import AddAlbumDialog from "./AddAlbumDialog";
import { Upload, Plus, X } from "lucide-react";
import { useRef, useState, useEffect } from "react";
import toast from "react-hot-toast";
import { getAudioDuration, formatDuration } from "@/utils/audioUtils";
import { Song } from "@/types";

interface EditSongDialogProps {
	song: Song;
	isOpen: boolean;
	onClose: () => void;
}

interface EditSong {
	title: string;
	artistId: string;
	featuredArtists: string;
	album: string;
	duration: string;
	releaseDate: string;
	composer: string;
	producer: string;
	source: string;
	credits: Array<{ name: string; role: string }>;
}

const EditSongDialog = ({ song, isOpen, onClose }: EditSongDialogProps) => {
	const { artists, editSong } = useMusicStore();
	const [isLoading, setIsLoading] = useState(false);
	const [addArtistDialogOpen, setAddArtistDialogOpen] = useState(false);
	const [addAlbumDialogOpen, setAddAlbumDialogOpen] = useState(false);
	const [detectedDuration, setDetectedDuration] = useState<number | null>(null);

	const [editedSong, setEditedSong] = useState<EditSong>({
		title: "",
		artistId: "",
		featuredArtists: "",
		album: "",
		duration: "0",
		releaseDate: "",
		composer: "",
		producer: "",
		source: "",
		credits: [],
	});

	const [currentCredit, setCurrentCredit] = useState({ name: "", role: "" });

	const [files, setFiles] = useState<{ audio: File | null; image: File | null }>({
		audio: null,
		image: null,
	});

	const audioInputRef = useRef<HTMLInputElement>(null);
	const imageInputRef = useRef<HTMLInputElement>(null);

	// Initialize form with song data
	useEffect(() => {
		if (song) {
			// If artistId is missing, try to find it by artist name
			let artistId = song.artistId || "";
			if (!artistId && song.artist) {
				const foundArtist = artists.find(artist => artist.name === song.artist);
				if (foundArtist) {
					artistId = foundArtist._id;
				}
			}

			setEditedSong({
				title: song.title,
				artistId: artistId,
				featuredArtists: song.featuredArtists?.join(", ") || "",
				album: song.albumId || "",
				duration: song.duration.toString(),
				releaseDate: song.releaseDate ? song.releaseDate.split('T')[0] : "",
				composer: song.composer || "",
				producer: song.producer || "",
				source: song.source || "",
				credits: song.credits || [],
			});
		}
	}, [song, artists]);

	const handleAudioFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			setFiles((prev) => ({ ...prev, audio: file }));
			
			try {
				const duration = await getAudioDuration(file);
				setDetectedDuration(duration);
				setEditedSong((prev) => ({ ...prev, duration: duration.toString() }));
				toast.success(`Duration detected: ${formatDuration(duration)}`);
			} catch (error) {
				console.error('Error detecting audio duration:', error);
				toast.error('Could not detect audio duration');
			}
		}
	};

	const handleAddCredit = () => {
		if (currentCredit.name.trim() && currentCredit.role.trim()) {
			setEditedSong({
				...editedSong,
				credits: [...editedSong.credits, { ...currentCredit }]
			});
			setCurrentCredit({ name: "", role: "" });
		}
	};

	const handleRemoveCredit = (index: number) => {
		setEditedSong({
			...editedSong,
			credits: editedSong.credits.filter((_, i) => i !== index)
		});
	};

	const handleSubmit = async () => {
		setIsLoading(true);

		try {
			if (!editedSong.artistId) {
				return toast.error("Please select an artist");
			}

			const selectedArtist = artists.find(a => a._id === editedSong.artistId);
			if (!selectedArtist) {
				return toast.error("Selected artist not found");
			}

			const formData = new FormData();

			formData.append("title", editedSong.title);
			formData.append("artist", selectedArtist.name);
			formData.append("artistId", editedSong.artistId);
			formData.append("duration", editedSong.duration);
			formData.append("releaseDate", editedSong.releaseDate);
			formData.append("composer", editedSong.composer);
			formData.append("producer", editedSong.producer);
			formData.append("source", editedSong.source);
			formData.append("credits", JSON.stringify(editedSong.credits));
			
			if (editedSong.featuredArtists.trim()) {
				formData.append("featuredArtists", editedSong.featuredArtists);
			}
			
			if (editedSong.album && editedSong.album !== "none") {
				formData.append("albumId", editedSong.album);
			}

			// Only append files if they were changed
			if (files.audio) {
				formData.append("audioFile", files.audio);
			}
			if (files.image) {
				formData.append("imageFile", files.image);
			}

			await editSong(song._id, formData);
			onClose();
		} catch (error: any) {
			toast.error("Failed to update song: " + error.message);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<>
			<Dialog open={isOpen} onOpenChange={onClose}>
				<DialogContent className='bg-zinc-900 border-zinc-700 max-h-[80vh] overflow-auto'>
					<DialogHeader>
						<DialogTitle className='text-white'>Edit Song</DialogTitle>
						<DialogDescription className='text-gray-400'>Update the song details</DialogDescription>
					</DialogHeader>

					<div className='space-y-4 py-4'>
						<input
							type='file'
							accept='audio/*'
							ref={audioInputRef}
							hidden
							onChange={handleAudioFileChange}
						/>

						<input
							type='file'
							ref={imageInputRef}
							className='hidden'
							accept='image/*'
							onChange={(e) => setFiles((prev) => ({ ...prev, image: e.target.files![0] }))}
						/>

						{/* Current image preview */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Current Image</label>
							<div className='flex items-center gap-4'>
								<img 
									src={song.imageUrl || '/placeholder-song.jpg'} 
									alt={song.title} 
									className='w-20 h-20 rounded object-cover' 
								/>
								<Button
									variant='outline'
									onClick={() => imageInputRef.current?.click()}
									className='flex items-center gap-2'
								>
									<Upload className='h-4 w-4' />
									{files.image ? 'Change Image' : 'Update Image'}
								</Button>
							</div>
							{files.image && (
								<div className='text-sm text-emerald-500'>
									New image selected: {files.image.name}
								</div>
							)}
						</div>

						{/* Audio file update */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Audio File</label>
							<div className='flex items-center gap-2'>
								<Button variant='outline' onClick={() => audioInputRef.current?.click()} className='w-full'>
									{files.audio ? files.audio.name.slice(0, 30) : "Update Audio File (Optional)"}
								</Button>
							</div>
							{files.audio && (
								<div className='text-sm text-emerald-500'>
									New audio file selected: {files.audio.name}
								</div>
							)}
						</div>

						{/* Title */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Title</label>
							<Input
								value={editedSong.title}
								onChange={(e) => setEditedSong({ ...editedSong, title: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
							/>
						</div>

						{/* Artist */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Artist</label>
							<ArtistCombobox
								value={editedSong.artistId}
								onChange={(value) => setEditedSong({ ...editedSong, artistId: value })}
								onAddArtist={() => setAddArtistDialogOpen(true)}
								placeholder="Select artist..."
							/>
						</div>

						{/* Featured Artists */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Featured Artists (Optional)</label>
							<Input
								value={editedSong.featuredArtists}
								onChange={(e) => setEditedSong({ ...editedSong, featuredArtists: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
								placeholder="e.g., Artist 1, Artist 2"
							/>
							<p className='text-xs text-zinc-500'>Separate multiple artists with commas</p>
						</div>

						{/* Duration */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Duration (seconds)</label>
							<Input
								type='number'
								min='0'
								value={editedSong.duration}
								onChange={(e) => setEditedSong({ ...editedSong, duration: e.target.value || "0" })}
								className='bg-zinc-800 border-zinc-700'
								placeholder={detectedDuration ? `Auto-detected: ${detectedDuration}s` : "Enter duration"}
							/>
							{detectedDuration && (
								<p className='text-xs text-emerald-400'>
									Auto-detected: {formatDuration(detectedDuration)}
								</p>
							)}
						</div>

						{/* Release Date */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Release Date</label>
							<Input
								type='date'
								value={editedSong.releaseDate}
								onChange={(e) => setEditedSong({ ...editedSong, releaseDate: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
							/>
						</div>

						{/* Composer */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Composer</label>
							<Input
								value={editedSong.composer}
								onChange={(e) => setEditedSong({ ...editedSong, composer: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
								placeholder="Who composed the song?"
							/>
						</div>

						{/* Producer */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Producer</label>
							<Input
								value={editedSong.producer}
								onChange={(e) => setEditedSong({ ...editedSong, producer: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
								placeholder="Who produced the song?"
							/>
						</div>

						{/* Source */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Source</label>
							<Input
								value={editedSong.source}
								onChange={(e) => setEditedSong({ ...editedSong, source: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
								placeholder="Source of the song (e.g., album, single, etc.)"
							/>
						</div>

						{/* Credits */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Credits</label>
							<div className='grid grid-cols-2 gap-2'>
								<Input
									value={currentCredit.name}
									onChange={(e) => setCurrentCredit({ ...currentCredit, name: e.target.value })}
									className='bg-zinc-800 border-zinc-700'
									placeholder="Person's name"
								/>
								<div className='flex gap-2'>
									<Input
										value={currentCredit.role}
										onChange={(e) => setCurrentCredit({ ...currentCredit, role: e.target.value })}
										className='bg-zinc-800 border-zinc-700'
										placeholder="Their role"
									/>
									<Button
										type='button'
										variant='outline'
										onClick={handleAddCredit}
										disabled={!currentCredit.name.trim() || !currentCredit.role.trim()}
									>
										<Plus className='h-4 w-4' />
									</Button>
								</div>
							</div>
							{editedSong.credits.length > 0 && (
								<div className='flex flex-wrap gap-2 mt-2'>
									{editedSong.credits.map((credit, index) => (
										<Badge key={index} variant="secondary" className='flex items-center gap-1'>
											{credit.name} ({credit.role})
											<X
												className='h-3 w-3 cursor-pointer hover:text-red-500'
												onClick={() => handleRemoveCredit(index)}
											/>
										</Badge>
									))}
								</div>
							)}
						</div>

						{/* Album */}
						<div className='space-y-2'>
							<label className='text-sm font-medium text-white'>Album (Optional)</label>
							<AlbumCombobox
								value={editedSong.album}
								onChange={(value) => setEditedSong({ ...editedSong, album: value })}
								onAddAlbum={() => setAddAlbumDialogOpen(true)}
								placeholder="Select album..."
							/>
						</div>
					</div>

					<DialogFooter>
						<Button variant='outline' onClick={onClose} disabled={isLoading}>
							Cancel
						</Button>
						<Button onClick={handleSubmit} disabled={isLoading}>
							{isLoading ? "Updating..." : "Update Song"}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
			
			{/* Embedded Add Artist Dialog */}
			<Dialog open={addArtistDialogOpen} onOpenChange={setAddArtistDialogOpen}>
				<DialogContent className='bg-zinc-900 border-zinc-700'>
					<AddArtistDialog 
						isOpen={addArtistDialogOpen} 
						onClose={() => setAddArtistDialogOpen(false)}
						embedded={true}
					/>
				</DialogContent>
			</Dialog>

			{/* Embedded Add Album Dialog */}
			<Dialog open={addAlbumDialogOpen} onOpenChange={setAddAlbumDialogOpen}>
				<DialogContent className='bg-zinc-900 border-zinc-700'>
					<AddAlbumDialog 
						isOpen={addAlbumDialogOpen} 
						onClose={() => setAddAlbumDialogOpen(false)}
						embedded={true}
					/>
				</DialogContent>
			</Dialog>
		</>
	);
};

export default EditSongDialog;
