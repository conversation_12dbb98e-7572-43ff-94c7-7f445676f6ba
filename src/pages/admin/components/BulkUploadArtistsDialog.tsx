import { Button } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	<PERSON><PERSON>Footer,
	<PERSON><PERSON>Header,
	<PERSON><PERSON><PERSON><PERSON>le,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useMusicStore } from "@/stores/useMusicStore";
import { Upload, Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import toast from "react-hot-toast";

interface BulkArtistData {
	name: string;
	bgColor: string;
	imageFile: File | null;
}

const BulkUploadArtistsDialog = () => {
	const { bulkUploadArtists } = useMusicStore();
	const [isOpen, setIsOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [artists, setArtists] = useState<BulkArtistData[]>([{
		name: "",
		bgColor: "#1f2937",
		imageFile: null,
	}]);

	const addArtist = () => {
		setArtists([...artists, {
			name: "",
			bgColor: "#1f2937",
			imageFile: null,
		}]);
	};

	const removeArtist = (index: number) => {
		setArtists(artists.filter((_, i) => i !== index));
	};

	const updateArtist = (index: number, field: keyof BulkArtistData, value: any) => {
		setArtists(artists.map((artist, i) => 
			i === index ? { ...artist, [field]: value } : artist
		));
	};

	const handleSubmit = async () => {
		setIsLoading(true);

		try {
			// Validate all artists
			for (let i = 0; i < artists.length; i++) {
				const artist = artists[i];
				if (!artist.imageFile) {
					return toast.error(`Artist ${i + 1}: Please upload an image file`);
				}
				if (!artist.name.trim()) {
					return toast.error(`Artist ${i + 1}: Please enter a name`);
				}
			}

			// Create FormData for each artist
			const formDataArray = artists.map((artist) => {
				const formData = new FormData();
				
				formData.append("name", artist.name);
				formData.append("bgColor", artist.bgColor);
				formData.append("imageFile", artist.imageFile!);

				return formData;
			});

			await bulkUploadArtists(formDataArray);

			// Reset form
			setArtists([{
				name: "",
				bgColor: "#1f2937",
				imageFile: null,
			}]);
			setIsOpen(false);
		} catch (error: any) {
			toast.error("Failed to bulk upload artists: " + error.message);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={setIsOpen}>
			<DialogTrigger asChild>
				<Button variant="outline" className="bg-orange-600 hover:bg-orange-700 text-white border-orange-600">
					<Upload className="mr-2 h-4 w-4" />
					Bulk Upload Artists
				</Button>
			</DialogTrigger>

			<DialogContent className="bg-zinc-900 border-zinc-700 max-w-4xl max-h-[80vh] overflow-auto">
				<DialogHeader>
					<DialogTitle className='text-white'>Bulk Upload Artists</DialogTitle>
					<DialogDescription>Upload multiple artists at once</DialogDescription>
				</DialogHeader>

				<div className="space-y-6 py-4">
					{artists.map((artist, index) => (
						<div key={index} className="p-4 border border-zinc-700 rounded-lg space-y-4">
							<div className="flex justify-between items-center">
								<h3 className="text-lg font-medium">Artist {index + 1}</h3>
								{artists.length > 1 && (
									<Button
										variant="ghost"
										size="sm"
										onClick={() => removeArtist(index)}
										className="text-red-400 hover:text-red-300"
									>
										<Trash2 className="h-4 w-4" />
									</Button>
								)}
							</div>

							<div className="grid grid-cols-2 gap-4">
								{/* Image File */}
								<div className="space-y-2 col-span-2">
									<label className="text-sm font-medium">Artist Image</label>
									<Input
										type="file"
										accept="image/*"
										onChange={(e) => updateArtist(index, 'imageFile', e.target.files?.[0] || null)}
										className="bg-zinc-800 border-zinc-700"
									/>
								</div>

								{/* Name */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Artist Name</label>
									<Input
										value={artist.name}
										onChange={(e) => updateArtist(index, 'name', e.target.value)}
										className="bg-zinc-800 border-zinc-700"
										placeholder="Enter artist name"
									/>
								</div>

								{/* Background Color */}
								<div className="space-y-2">
									<label className="text-sm font-medium">Background Color</label>
									<div className="flex items-center gap-2">
										<Input
											type="color"
											value={artist.bgColor}
											onChange={(e) => updateArtist(index, 'bgColor', e.target.value)}
											className="w-16 h-10 bg-zinc-800 border-zinc-700 rounded cursor-pointer"
										/>
										<Input
											value={artist.bgColor}
											onChange={(e) => updateArtist(index, 'bgColor', e.target.value)}
											className="flex-1 bg-zinc-800 border-zinc-700"
											placeholder="#1f2937"
										/>
									</div>
								</div>
							</div>
						</div>
					))}

					<Button
						onClick={addArtist}
						variant="outline"
						className="w-full border-dashed border-zinc-600 text-zinc-400 hover:text-zinc-300"
					>
						<Plus className="mr-2 h-4 w-4" />
						Add Another Artist
					</Button>
				</div>

				<DialogFooter>
					<Button variant="outline" onClick={() => setIsOpen(false)} disabled={isLoading}>
						Cancel
					</Button>
					<Button onClick={handleSubmit} disabled={isLoading}>
						{isLoading ? "Uploading..." : `Upload ${artists.length} Artist${artists.length > 1 ? 's' : ''}`}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};

export default BulkUploadArtistsDialog;
