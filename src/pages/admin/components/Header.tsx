import { signOut } from "@/lib/auth-client";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { LogOut } from "lucide-react";

const Header = () => {
	return (
		<div className='flex items-center justify-between'>
			<div className='flex items-center gap-3 mb-8'>
				<Link to='/' className='rounded-lg'>
					<img src='/spotify.png' className='size-10' />
				</Link>
				<div>
					<h1 className='text-3xl font-bold text-white'>Music Manager</h1>
					<p className='text-zinc-400 mt-1'>Manage your music catalog</p>
				</div>
			</div>
			<Button variant="outline" onClick={() => signOut()}>
				<LogOut className='size-4 mr-2' />
				Sign Out
			</Button>
		</div>
	);
};
export default Header;
