import { Button } from "@/components/ui/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	Di<PERSON>Footer,
	<PERSON><PERSON>Header,
	<PERSON><PERSON>Title,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useMusicStore } from "@/stores/useMusicStore";
import { Plus, Upload, X } from "lucide-react";
import { useRef, useState } from "react";
import toast from "react-hot-toast";

interface AddArtistDialogProps {
	isOpen?: boolean;
	onClose?: () => void;
	embedded?: boolean;
}

const AddArtistDialog = ({ isOpen, onClose, embedded = false }: AddArtistDialogProps = {}) => {
	const { addArtist } = useMusicStore();
	const [artistDialogOpen, setArtistDialogOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const [newArtist, setNewArtist] = useState({
		name: "",
		bgColor: "#1f2937",
		bio: "",
		monthlyListeners: 0,
		genres: [] as string[],
		socialLinks: {
			spotify: "",
			instagram: "",
			twitter: "",
			website: "",
		},
	});

	const [currentGenre, setCurrentGenre] = useState("");

	const [imageFile, setImageFile] = useState<File | null>(null);

	const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			setImageFile(file);
		}
	};

	const handleAddGenre = () => {
		if (currentGenre.trim() && !newArtist.genres.includes(currentGenre.trim())) {
			setNewArtist({
				...newArtist,
				genres: [...newArtist.genres, currentGenre.trim()]
			});
			setCurrentGenre("");
		}
	};

	const handleRemoveGenre = (genre: string) => {
		setNewArtist({
			...newArtist,
			genres: newArtist.genres.filter(g => g !== genre)
		});
	};

	const handleGenreKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter') {
			e.preventDefault();
			handleAddGenre();
		}
	};

	const handleSubmit = async () => {
		setIsLoading(true);

		try {
			if (!imageFile) {
				return toast.error("Please upload an image");
			}

			const formData = new FormData();
			formData.append("name", newArtist.name);
			formData.append("bgColor", newArtist.bgColor);
			formData.append("about", newArtist.bio);
			formData.append("monthlyListeners", newArtist.monthlyListeners.toString());
			formData.append("genres", JSON.stringify(newArtist.genres));
			formData.append("socialLinks", JSON.stringify(newArtist.socialLinks));
			formData.append("imageFile", imageFile);

			// Use the store function which handles optimistic updates
			await addArtist(formData);

			// Reset form
			setNewArtist({
				name: "",
				bgColor: "#1f2937",
				bio: "",
				monthlyListeners: 0,
				genres: [],
				socialLinks: {
					spotify: "",
					instagram: "",
					twitter: "",
					website: "",
				},
			});
			setCurrentGenre("");
			setImageFile(null);
			if (embedded && onClose) {
				onClose();
			} else {
				setArtistDialogOpen(false);
			}
		} catch (error: any) {
			// Error handling is done in the store
		} finally {
			setIsLoading(false);
		}
	};

	if (embedded && isOpen) {
		return (
			<>
				<DialogHeader>
					<DialogTitle className='text-white'>Add New Artist</DialogTitle>
					<DialogDescription className='text-gray-400'>Add a new artist to your platform</DialogDescription>
				</DialogHeader>
				<div className='space-y-4 py-4 max-h-[60vh] overflow-y-auto'>
					<input
						type='file'
						ref={fileInputRef}
						onChange={handleImageSelect}
						accept='image/*'
						className='hidden'
					/>
					<div
						className='flex items-center justify-center p-6 border-2 border-dashed border-zinc-700 rounded-lg cursor-pointer'
						onClick={() => fileInputRef.current?.click()}
					>
						<div className='text-center'>
							<div className='p-3 bg-zinc-800 rounded-full inline-block mb-2'>
								<Upload className='h-6 w-6 text-zinc-400' />
							</div>
							<div className='text-sm text-zinc-400 mb-2'>
								{imageFile ? imageFile.name : "Upload artist image"}
							</div>
							<Button variant='outline' size='sm' className='text-xs'>
								Choose File
							</Button>
						</div>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Artist Name</label>
						<Input
							value={newArtist.name}
							onChange={(e) => setNewArtist({ ...newArtist, name: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Enter artist name'
						/>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Background Color</label>
						<div className='flex items-center gap-2'>
							<input
								type='color'
								value={newArtist.bgColor}
								onChange={(e) => setNewArtist({ ...newArtist, bgColor: e.target.value })}
								className='w-12 h-10 rounded border-zinc-700 bg-zinc-800'
							/>
							<Input
								value={newArtist.bgColor}
								onChange={(e) => setNewArtist({ ...newArtist, bgColor: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
								placeholder='Background color'
							/>
						</div>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Bio/About</label>
						<Textarea
							value={newArtist.bio}
							onChange={(e) => setNewArtist({ ...newArtist, bio: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Tell us about the artist...'
							rows={3}
						/>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Monthly Listeners</label>
						<Input
							type='number'
							min='0'
							value={newArtist.monthlyListeners}
							onChange={(e) => setNewArtist({ ...newArtist, monthlyListeners: parseInt(e.target.value) || 0 })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Number of monthly listeners'
						/>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Genres</label>
						<div className='flex gap-2'>
							<Input
								value={currentGenre}
								onChange={(e) => setCurrentGenre(e.target.value)}
								onKeyPress={handleGenreKeyPress}
								className='bg-zinc-800 border-zinc-700'
								placeholder='Add a genre'
							/>
							<Button
								type='button'
								variant='outline'
								onClick={handleAddGenre}
								disabled={!currentGenre.trim() || newArtist.genres.includes(currentGenre.trim())}
							>
								<Plus className='h-4 w-4' />
							</Button>
						</div>
						{newArtist.genres.length > 0 && (
							<div className='flex flex-wrap gap-2 mt-2'>
								{newArtist.genres.map((genre) => (
									<Badge key={genre} variant="secondary" className='flex items-center gap-1'>
										{genre}
										<X
											className='h-3 w-3 cursor-pointer hover:text-red-500'
											onClick={() => handleRemoveGenre(genre)}
										/>
									</Badge>
								))}
							</div>
						)}
					</div>

					<div className='space-y-3'>
						<label className='text-sm font-medium'>Social Links</label>
						<div className='grid grid-cols-2 gap-3'>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Spotify</label>
								<Input
									value={newArtist.socialLinks.spotify}
									onChange={(e) => setNewArtist({ 
										...newArtist, 
										socialLinks: { ...newArtist.socialLinks, spotify: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Spotify URL'
								/>
							</div>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Instagram</label>
								<Input
									value={newArtist.socialLinks.instagram}
									onChange={(e) => setNewArtist({ 
										...newArtist, 
										socialLinks: { ...newArtist.socialLinks, instagram: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Instagram URL'
								/>
							</div>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Twitter</label>
								<Input
									value={newArtist.socialLinks.twitter}
									onChange={(e) => setNewArtist({ 
										...newArtist, 
										socialLinks: { ...newArtist.socialLinks, twitter: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Twitter URL'
								/>
							</div>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Website</label>
								<Input
									value={newArtist.socialLinks.website}
									onChange={(e) => setNewArtist({ 
										...newArtist, 
										socialLinks: { ...newArtist.socialLinks, website: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Website URL'
								/>
							</div>
						</div>
					</div>
				</div>
				<DialogFooter>
					<Button variant='outline' onClick={onClose} disabled={isLoading}>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						className='bg-orange-500 hover:bg-orange-600'
						disabled={isLoading || !imageFile || !newArtist.name}
					>
						{isLoading ? "Creating..." : "Add Artist"}
					</Button>
				</DialogFooter>
			</>
		);
	}

	return (
		<Dialog open={artistDialogOpen} onOpenChange={setArtistDialogOpen}>
			<DialogTrigger asChild>
				<Button className='bg-orange-500 hover:bg-orange-600 text-white'>
					<Plus className='mr-2 h-4 w-4' />
					Add Artist
				</Button>
			</DialogTrigger>
			<DialogContent className='bg-zinc-900 border-zinc-700 max-h-[90vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='text-white'>Add New Artist</DialogTitle>
					<DialogDescription className='text-gray-400'>Add a new artist to your platform</DialogDescription>
				</DialogHeader>
				<div className='space-y-4 py-4'>
					<input
						type='file'
						ref={fileInputRef}
						onChange={handleImageSelect}
						accept='image/*'
						className='hidden'
					/>
					<div
						className='flex items-center justify-center p-6 border-2 border-dashed border-zinc-700 rounded-lg cursor-pointer'
						onClick={() => fileInputRef.current?.click()}
					>
						<div className='text-center'>
							<div className='p-3 bg-zinc-800 rounded-full inline-block mb-2'>
								<Upload className='h-6 w-6 text-zinc-400' />
							</div>
							<div className='text-sm text-zinc-400 mb-2'>
								{imageFile ? imageFile.name : "Upload artist image"}
							</div>
							<Button variant='outline' size='sm' className='text-xs'>
								Choose File
							</Button>
						</div>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Artist Name</label>
						<Input
							value={newArtist.name}
							onChange={(e) => setNewArtist({ ...newArtist, name: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Enter artist name'
						/>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Background Color</label>
						<div className='flex items-center gap-2'>
							<input
								type='color'
								value={newArtist.bgColor}
								onChange={(e) => setNewArtist({ ...newArtist, bgColor: e.target.value })}
								className='w-12 h-10 rounded border-zinc-700 bg-zinc-800'
							/>
							<Input
								value={newArtist.bgColor}
								onChange={(e) => setNewArtist({ ...newArtist, bgColor: e.target.value })}
								className='bg-zinc-800 border-zinc-700'
								placeholder='Background color'
							/>
						</div>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Bio/About</label>
						<Textarea
							value={newArtist.bio}
							onChange={(e) => setNewArtist({ ...newArtist, bio: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Tell us about the artist...'
							rows={3}
						/>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Monthly Listeners</label>
						<Input
							type='number'
							min='0'
							value={newArtist.monthlyListeners}
							onChange={(e) => setNewArtist({ ...newArtist, monthlyListeners: parseInt(e.target.value) || 0 })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Number of monthly listeners'
						/>
					</div>

					<div className='space-y-2'>
						<label className='text-sm font-medium'>Genres</label>
						<div className='flex gap-2'>
							<Input
								value={currentGenre}
								onChange={(e) => setCurrentGenre(e.target.value)}
								onKeyPress={handleGenreKeyPress}
								className='bg-zinc-800 border-zinc-700'
								placeholder='Add a genre'
							/>
							<Button
								type='button'
								variant='outline'
								onClick={handleAddGenre}
								disabled={!currentGenre.trim() || newArtist.genres.includes(currentGenre.trim())}
							>
								<Plus className='h-4 w-4' />
							</Button>
						</div>
						{newArtist.genres.length > 0 && (
							<div className='flex flex-wrap gap-2 mt-2'>
								{newArtist.genres.map((genre) => (
									<Badge key={genre} variant="secondary" className='flex items-center gap-1'>
										{genre}
										<X
											className='h-3 w-3 cursor-pointer hover:text-red-500'
											onClick={() => handleRemoveGenre(genre)}
										/>
									</Badge>
								))}
							</div>
						)}
					</div>

					<div className='space-y-3'>
						<label className='text-sm font-medium'>Social Links</label>
						<div className='grid grid-cols-2 gap-3'>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Spotify</label>
								<Input
									value={newArtist.socialLinks.spotify}
									onChange={(e) => setNewArtist({ 
										...newArtist, 
										socialLinks: { ...newArtist.socialLinks, spotify: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Spotify URL'
								/>
							</div>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Instagram</label>
								<Input
									value={newArtist.socialLinks.instagram}
									onChange={(e) => setNewArtist({ 
										...newArtist, 
										socialLinks: { ...newArtist.socialLinks, instagram: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Instagram URL'
								/>
							</div>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Twitter</label>
								<Input
									value={newArtist.socialLinks.twitter}
									onChange={(e) => setNewArtist({ 
										...newArtist, 
										socialLinks: { ...newArtist.socialLinks, twitter: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Twitter URL'
								/>
							</div>
							<div className='space-y-1'>
								<label className='text-xs text-zinc-400'>Website</label>
								<Input
									value={newArtist.socialLinks.website}
									onChange={(e) => setNewArtist({ 
										...newArtist, 
										socialLinks: { ...newArtist.socialLinks, website: e.target.value }
									})}
									className='bg-zinc-800 border-zinc-700'
									placeholder='Website URL'
								/>
							</div>
						</div>
					</div>
				</div>
				<DialogFooter>
					<Button variant='outline' onClick={() => setArtistDialogOpen(false)} disabled={isLoading}>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						className='bg-orange-500 hover:bg-orange-600'
						disabled={isLoading || !imageFile || !newArtist.name}
					>
						{isLoading ? "Creating..." : "Add Artist"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};
export default AddArtistDialog;
