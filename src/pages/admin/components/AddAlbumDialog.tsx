import { Button } from "@/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	Di<PERSON>Header,
	<PERSON><PERSON>Title,
	DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useMusicStore } from "@/stores/useMusicStore";
import { ArtistCombobox } from "@/components/ArtistCombobox";
import AddArtistDialog from "./AddArtistDialog";
import { Plus, Upload } from "lucide-react";
import { useRef, useState } from "react";
import toast from "react-hot-toast";

interface AddAlbumDialogProps {
	isOpen?: boolean;
	onClose?: () => void;
	embedded?: boolean;
}

const AddAlbumDialog = ({ isOpen, onClose, embedded = false }: AddAlbumDialogProps = {}) => {
	const { artists, addAlbum } = useMusicStore();
	const [albumDialogOpen, setAlbumDialogOpen] = useState(false);
	const [addArtistDialogOpen, setAddArtistDialogOpen] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const [newAlbum, setNewAlbum] = useState({
		title: "",
		artistId: "",
		releaseYear: new Date().getFullYear(),
		genre: "",
	});

	const [imageFile, setImageFile] = useState<File | null>(null);

	const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			setImageFile(file);
		}
	};

	const handleSubmit = async () => {
		setIsLoading(true);

		try {
			if (!imageFile) {
				return toast.error("Please upload an image");
			}

			if (!newAlbum.artistId) {
				return toast.error("Please select an artist");
			}

			const selectedArtist = artists.find(a => a._id === newAlbum.artistId);
			if (!selectedArtist) {
				return toast.error("Selected artist not found");
			}

			const formData = new FormData();
			formData.append("title", newAlbum.title);
			formData.append("artist", selectedArtist.name); // Still send artist name for backward compatibility
			formData.append("artistId", newAlbum.artistId);
			formData.append("releaseYear", newAlbum.releaseYear.toString());
			formData.append("genre", newAlbum.genre);
			formData.append("imageFile", imageFile);

			await addAlbum(formData);

			setNewAlbum({
				title: "",
				artistId: "",
				releaseYear: new Date().getFullYear(),
				genre: "",
			});
			setImageFile(null);
			if (embedded && onClose) {
				onClose();
			} else {
				setAlbumDialogOpen(false);
			}
		} catch (error: any) {
			toast.error("Failed to create album: " + error.message);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<>
		{embedded ? (
			isOpen && (
			<div className="space-y-6">
				<div>
					<h3 className="text-lg font-semibold">Add New Album</h3>
					<p className="text-sm text-zinc-400">Create a new album for your music library</p>
				</div>

				<div className='space-y-4'>
					<input
						type='file'
						ref={fileInputRef}
						onChange={handleImageSelect}
						accept='image/*'
						className='hidden'
					/>
					<div
						className='flex items-center justify-center p-6 border-2 border-dashed border-zinc-700 rounded-lg cursor-pointer'
						onClick={() => fileInputRef.current?.click()}
					>
						<div className='text-center'>
							<div className='p-3 bg-zinc-800 rounded-full inline-block mb-2'>
								<Upload className='h-6 w-6 text-zinc-400' />
							</div>
							<div className='text-sm text-zinc-400 mb-2'>
								{imageFile ? imageFile.name : "Upload album artwork"}
							</div>
							<Button variant='outline' size='sm' className='text-xs'>
								Choose File
							</Button>
						</div>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Album Title</label>
						<Input
							value={newAlbum.title}
							onChange={(e) => setNewAlbum({ ...newAlbum, title: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Enter album title'
						/>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Artist</label>
						<ArtistCombobox
							value={newAlbum.artistId}
							onChange={(value) => setNewAlbum({ ...newAlbum, artistId: value })}
							onAddArtist={() => setAddArtistDialogOpen(true)}
							placeholder="Select artist..."
						/>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Genre</label>
						<Input
							value={newAlbum.genre}
							onChange={(e) => setNewAlbum({ ...newAlbum, genre: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Enter genre (e.g., Pop, Rock, Hip-Hop)'
						/>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Release Year</label>
						<Input
							type='number'
							value={newAlbum.releaseYear}
							onChange={(e) => setNewAlbum({ ...newAlbum, releaseYear: parseInt(e.target.value) })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Enter release year'
							min={1900}
							max={new Date().getFullYear()}
						/>
					</div>
				</div>

				<div className="flex justify-end gap-2">
					<Button variant='outline' onClick={onClose} disabled={isLoading}>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						className='bg-violet-500 hover:bg-violet-600'
						disabled={isLoading || !imageFile || !newAlbum.title || !newAlbum.artistId || !newAlbum.genre}
					>
						{isLoading ? "Creating..." : "Add Album"}
					</Button>
				</div>
			</div>
			)
		) : (
		<Dialog open={albumDialogOpen} onOpenChange={setAlbumDialogOpen}>
			<DialogTrigger asChild>
				<Button className='bg-violet-500 hover:bg-violet-600 text-white'>
					<Plus className='mr-2 h-4 w-4' />
					Add Album
				</Button>
			</DialogTrigger>
			<DialogContent className='bg-zinc-900 border-zinc-700'>
				<DialogHeader>
					<DialogTitle className='text-white'>Add New Album</DialogTitle>
					<DialogDescription>Add a new album to your collection</DialogDescription>
				</DialogHeader>
				<div className='space-y-4 py-4'>
					<input
						type='file'
						ref={fileInputRef}
						onChange={handleImageSelect}
						accept='image/*'
						className='hidden'
					/>
					<div
						className='flex items-center justify-center p-6 border-2 border-dashed border-zinc-700 rounded-lg cursor-pointer'
						onClick={() => fileInputRef.current?.click()}
					>
						<div className='text-center'>
							<div className='p-3 bg-zinc-800 rounded-full inline-block mb-2'>
								<Upload className='h-6 w-6 text-zinc-400' />
							</div>
							<div className='text-sm text-zinc-400 mb-2'>
								{imageFile ? imageFile.name : "Upload album artwork"}
							</div>
							<Button variant='outline' size='sm' className='text-xs'>
								Choose File
							</Button>
						</div>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Album Title</label>
						<Input
							value={newAlbum.title}
							onChange={(e) => setNewAlbum({ ...newAlbum, title: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Enter album title'
						/>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Artist</label>
						<ArtistCombobox
							value={newAlbum.artistId}
							onChange={(value) => setNewAlbum({ ...newAlbum, artistId: value })}
							onAddArtist={() => setAddArtistDialogOpen(true)}
							placeholder="Select artist..."
						/>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Genre</label>
						<Input
							value={newAlbum.genre}
							onChange={(e) => setNewAlbum({ ...newAlbum, genre: e.target.value })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Enter genre (e.g., Pop, Rock, Hip-Hop)'
						/>
					</div>
					<div className='space-y-2'>
						<label className='text-sm font-medium'>Release Year</label>
						<Input
							type='number'
							value={newAlbum.releaseYear}
							onChange={(e) => setNewAlbum({ ...newAlbum, releaseYear: parseInt(e.target.value) })}
							className='bg-zinc-800 border-zinc-700'
							placeholder='Enter release year'
							min={1900}
							max={new Date().getFullYear()}
						/>
					</div>
				</div>
				<DialogFooter>
					<Button variant='outline' onClick={() => setAlbumDialogOpen(false)} disabled={isLoading}>
						Cancel
					</Button>
					<Button
						onClick={handleSubmit}
						className='bg-violet-500 hover:bg-violet-600'
						disabled={isLoading || !imageFile || !newAlbum.title || !newAlbum.artistId || !newAlbum.genre}
					>
						{isLoading ? "Creating..." : "Add Album"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
		)}
		
		{/* Embedded Add Artist Dialog */}
		<Dialog open={addArtistDialogOpen} onOpenChange={setAddArtistDialogOpen}>
			<DialogContent className='bg-zinc-900 border-zinc-700'>
				<AddArtistDialog 
					isOpen={addArtistDialogOpen} 
					onClose={() => setAddArtistDialogOpen(false)}
					embedded={true}
				/>
			</DialogContent>
		</Dialog>
		</>
	);
};
export default AddAlbumDialog;
