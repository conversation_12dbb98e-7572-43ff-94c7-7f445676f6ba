/* Custom shadow styles following the design system */
.shadow-soft {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.shadow-elevated {
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.shadow-dramatic {
  box-shadow: 0 16px 48px rgba(0,0,0,0.2);
}

.group:hover .shadow-elevated {
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

/* Primary color utilities */
.text-primary {
  color: #D9AD39;
}

.bg-primary {
  background-color: #D9AD39;
}

.border-primary {
  border-color: #D9AD39;
}

/* Animation styles */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-fast {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-slow {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-scale-small:hover {
  transform: scale(1.05);
}

/* Backdrop blur for glass effect */
.backdrop-blur {
  backdrop-filter: blur(10px);
}

/* Custom gradient overlays */
.gradient-overlay {
  background: linear-gradient(135deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.2) 100%);
}

.gradient-overlay-light {
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
}

/* Primary gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #D9AD39 0%, #B8941F 100%);
}

.gradient-primary-light {
  background: linear-gradient(135deg, #D9AD39/20 0%, #B8941F/10 100%);
}

/* Mobile-specific utilities */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
}

.tap-highlight-none {
  -webkit-tap-highlight-color: transparent;
}

/* Spotify-style animations */
.spotify-bounce {
  animation: spotifyBounce 0.6s ease-in-out;
}

@keyframes spotifyBounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.spotify-pulse {
  animation: spotifyPulse 2s infinite;
}

@keyframes spotifyPulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Mobile card hover effects */
.mobile-card-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-card-hover:active {
  transform: scale(0.98);
  background-color: rgba(0, 0, 0, 0.05);
}

/* Swipe indicators */
.swipe-indicator {
  position: relative;
}

.swipe-indicator::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #D9AD39, transparent);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.swipe-indicator.active::after {
  opacity: 1;
}

/* Mini player animations */
.mini-player-slide-up {
  animation: slideUpFromBottom 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUpFromBottom {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Pull to refresh styles */
.pull-to-refresh-indicator {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Bottom navigation styles */
.bottom-nav-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Mobile typography enhancements */
.mobile-heading {
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.mobile-body {
  font-family: "SF Pro Text", -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
}

.mobile-caption {
  font-family: "SF Pro Text", -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 1.2;
}

/* Enhanced Mobile Interactions and Haptic Feedback */

/* Haptic feedback simulation */
@keyframes haptic-feedback {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

@keyframes smooth-press {
  0% { transform: scale(1) translateZ(0); }
  100% { transform: scale(0.97) translateZ(0); }
}

.haptic-light {
  animation: haptic-feedback 0.1s ease-out;
}

.haptic-medium {
  animation: haptic-feedback 0.15s ease-out;
}

.haptic-heavy {
  animation: haptic-feedback 0.2s ease-out;
}

/* Smooth mobile interactions */
.smooth-press {
  transition: transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.smooth-press:active {
  animation: smooth-press 0.1s ease-out;
}

/* Enhanced button interactions */
.mobile-button {
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateZ(0); /* Enable hardware acceleration */
}

.mobile-button:active {
  transform: scale(0.95) translateZ(0);
  transition-duration: 0.05s;
}

.mobile-button:hover {
  transform: scale(1.02) translateZ(0);
}

/* Smooth scrolling for mobile */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Loading states with smooth animations */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Enhanced focus states for accessibility */
.focus-visible {
  outline: 2px solid #1DB954;
  outline-offset: 2px;
}

/* Micro-interactions */
.micro-bounce {
  transition: transform 0.1s ease-out;
}

.micro-bounce:active {
  transform: scale(0.98);
}

/* Playing indicator animation */
@keyframes playing-bars {
  0%, 100% { height: 4px; }
  25% { height: 12px; }
  50% { height: 8px; }
  75% { height: 16px; }
}

.playing-bar {
  animation: playing-bars 1s ease-in-out infinite;
}

.playing-bar:nth-child(2) {
  animation-delay: 0.2s;
}

.playing-bar:nth-child(3) {
  animation-delay: 0.4s;
}

/* Mobile touch optimizations */
.mobile-touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Remove tap highlights on mobile */
.no-tap-highlight {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Pull to refresh styles */
.pull-to-refresh {
  transition: transform 0.3s ease-out;
}

.pull-to-refresh.pulling {
  transform: translateY(60px);
}

/* Swipe gesture feedback */
.swipe-feedback {
  transition: transform 0.2s ease-out;
}

.swipe-feedback.swiping-left {
  transform: translateX(-20px);
}

.swipe-feedback.swiping-right {
  transform: translateX(20px);
}

/* Hide scrollbars for horizontal scroll sections */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Tablet-specific responsive utilities */
@media (min-width: 768px) and (max-width: 1023px) {
  .tablet-flex-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: flex-start;
  }

  .tablet-card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .tablet-card-item {
    flex: 0 0 auto;
    width: 160px; /* Fixed width for consistent card sizing */
    min-width: 160px;
    max-width: 160px;
  }

  .tablet-featured-card {
    flex: 0 0 calc(50% - 0.375rem); /* Two cards per row with gap */
    min-width: 280px;
    max-width: calc(50% - 0.375rem);
  }

  /* Ensure no horizontal overflow on tablet */
  .tablet-no-overflow {
    overflow-x: hidden;
    max-width: 100%;
  }

  /* Tablet-specific spacing */
  .tablet-section-spacing {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    margin-bottom: 2rem;
  }

  /* Tablet card hover effects */
  .tablet-card-hover {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .tablet-card-hover:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  }

  /* Responsive grid for tablet quick access */
  .tablet-quick-access-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: flex-start;
  }

  .tablet-quick-access-item {
    flex: 0 0 calc(50% - 0.375rem);
    min-width: 280px;
    max-width: calc(50% - 0.375rem);
  }
}

/* Enhanced flex-wrap utilities for all screen sizes */
.flex-wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: flex-start;
  align-items: flex-start;
  overflow-x: hidden; /* Prevent horizontal overflow */
  max-width: 100%;
}

.flex-wrap-item {
  flex: 0 0 auto;
}

/* Responsive card sizing */
.responsive-card {
  width: 160px;
  min-width: 160px;
  max-width: 160px;
}

@media (min-width: 768px) and (max-width: 1023px) {
  .responsive-card {
    width: 160px;
    min-width: 160px;
    max-width: 160px;
  }
}

@media (min-width: 1024px) {
  .responsive-card {
    width: 180px;
    min-width: 180px;
    max-width: 180px;
  }
}

/* Tablet-specific animations */
@media (min-width: 768px) and (max-width: 1023px) {
  .tablet-smooth-transition {
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .tablet-hover-scale:hover {
    transform: scale(1.05);
  }

  .tablet-hover-shadow:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  }
}

/* Prevent content from extending beyond screen boundaries */
.viewport-constrained {
  max-width: 100vw;
  overflow-x: hidden;
}

.content-constrained {
  max-width: 100%;
  overflow-x: hidden;
}
