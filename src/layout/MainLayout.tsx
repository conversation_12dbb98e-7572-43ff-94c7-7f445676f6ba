import { ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable";
import { Outlet } from "react-router-dom";
import LeftSidebar from "./components/LeftSidebar";
import RightSidebar from "./components/RightSidebar";
import AudioPlayer from "./components/AudioPlayer";
import { PlaybackControls } from "./components/PlaybackControls";
import { useEffect, useState } from "react";
import MobileBottomNav from "./components/MobileBottomNav";
import MobileHeader from "./components/MobileHeader";
import VolumeControls from "./components/VolumeControls";
import { usePlayerStore } from "@/stores/usePlayerStore";
import TabletPlaybackControls from "./components/TabletPlaybackControls";
import Topbar from "@/components/Topbar";
import { useMusicStore } from "@/stores/useMusicStore";

type ViewMode = "mobile" | "tablet" | "desktop";

const MainLayout = () => {
  const [viewMode, setViewMode] = useState<ViewMode>("desktop");
  const { currentSong, initializeQueue } = usePlayerStore();
  const [volume, setVolume] = useState(50);
  const {
      fetchFeaturedSongs,
      fetchMadeForYouSongs,
      fetchTrendingSongs,
      isLoading,
      madeForYouSongs,
      featuredSongs,
      trendingSongs,
    } = useMusicStore();

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0]);
  };

  useEffect(() => {
    const checkViewMode = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setViewMode("mobile");
      } else if (width < 1024) {
        setViewMode("tablet");
      } else {
        setViewMode("desktop");
      }
    };

    checkViewMode();
    window.addEventListener("resize", checkViewMode);
    return () => window.removeEventListener("resize", checkViewMode);
  }, []);

  useEffect(() => {
      fetchFeaturedSongs();
      fetchMadeForYouSongs();
      fetchTrendingSongs();
    }, [fetchFeaturedSongs, fetchMadeForYouSongs, fetchTrendingSongs]);
  
    useEffect(() => {
      if (
        madeForYouSongs.length > 0 &&
        featuredSongs.length > 0 &&
        trendingSongs.length > 0
      ) {
        const allSongs = [...featuredSongs, ...madeForYouSongs, ...trendingSongs];
        initializeQueue(allSongs);
      }
    }, [initializeQueue, madeForYouSongs, trendingSongs, featuredSongs]);

  if (viewMode === "mobile") {
    return (
      <div className="h-screen bg-white text-black flex flex-col">
        <AudioPlayer />

        {/* Mobile Header */}
        <MobileHeader />

        {/* Main Content - Adjusted padding to account for removed mini-player */}
        <main className="flex-1 overflow-hidden pt-16 pb-20">
          <Outlet />
        </main>

        {/* Enhanced Bottom Navigation with integrated playback info */}
        <MobileBottomNav />
      </div>
    );
  }

  if (viewMode === "tablet") {
    return (
      <div className="h-screen bg-white text-black flex flex-col">
        <AudioPlayer />

        {/* Tablet Header */}
        <MobileHeader title="Music" />

        {/* Main Content - Full width without sidebar */}
        <main className="flex-1 overflow-y-auto bg-gray-50/30 pt-16">
          <div className="max-w-6xl mx-auto p-6">
            <Outlet />
          </div>
        </main>

        {/* Enhanced Tablet Playback Controls */}
        <PlaybackControls />
      </div>
    );
  }

  // Desktop layout
  return (
    <div className="h-screen bg-white text-black flex flex-col">
      <ResizablePanelGroup
        direction="horizontal"
        className="flex-1 flex h-full overflow-hidden p-2"
      >
        <AudioPlayer />

        <ResizablePanel defaultSize={20} minSize={10} maxSize={30}>
          <LeftSidebar />
        </ResizablePanel>

        <ResizablePanel defaultSize={60}>
          <Topbar />
          <Outlet />
        </ResizablePanel>

        <RightSidebar />
      </ResizablePanelGroup>

      <PlaybackControls />
    </div>
  );
};
export default MainLayout;
