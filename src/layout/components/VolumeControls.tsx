import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Laptop2, ListMusic, Mic2, Volume1 } from "lucide-react";

interface VolumeControlsProps {
  volume: number;
  onVolumeChange: (value: number[]) => void;
  className?: string;
}

const VolumeControls = ({ volume, onVolumeChange, className = "" }: VolumeControlsProps) => {
  return (
    <div className={`flex items-center gap-4 min-w-[180px] w-[30%] justify-end ${className}`}>
      <Button
        size="icon"
        variant="ghost"
        className="hover:text-black text-zinc-600"
      >
        <Mic2 className="h-4 w-4" />
      </Button>
      <Button
        size="icon"
        variant="ghost"
        className="hover:text-black text-zinc-600"
      >
        <ListMusic className="h-4 w-4" />
      </Button>
      <Button
        size="icon"
        variant="ghost"
        className="hover:text-black text-zinc-600"
      >
        <Laptop2 className="h-4 w-4" />
      </Button>

      <div className="flex items-center gap-2">
        <Button
          size="icon"
          variant="ghost"
          className="hover:text-black text-zinc-600"
        >
          <Volume1 className="h-4 w-4" />
        </Button>

        <Slider
          value={[volume]}
          max={100}
          step={1}
          className="w-24 hover:cursor-grab active:cursor-grabbing"
          onValueChange={onVolumeChange}
        />
      </div>
    </div>
  );
};

export default VolumeControls;
