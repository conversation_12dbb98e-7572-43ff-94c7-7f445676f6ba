import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { Pause, Play, SkipBack, SkipForward, Shuffle, Repeat } from "lucide-react";

interface PlayerControlsProps {
  currentTime: number;
  duration: number;
  onSeek: (value: number[]) => void;
  formatTime: (seconds: number) => string;
  className?: string;
  isCompact?: boolean;
}

const PlayerControls = ({
  currentTime,
  duration,
  onSeek,
  formatTime,
  className = "",
  isCompact = false
}: PlayerControlsProps) => {
  const { currentSong, isPlaying, togglePlay, playNext, playPrevious } = usePlayerStore();

  if (isCompact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Button
          size="icon"
          variant="ghost"
          className="hover:text-black text-zinc-600 h-8 w-8"
          onClick={playPrevious}
          disabled={!currentSong}
        >
          <SkipBack className="h-4 w-4" />
        </Button>

        <Button
          size="icon"
          className="bg-primary hover:bg-primary/80 text-white rounded-full h-8 w-8"
          onClick={togglePlay}
          disabled={!currentSong}
        >
          {isPlaying ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4" />
          )}
        </Button>
        
        <Button
          size="icon"
          variant="ghost"
          className="hover:text-black text-zinc-600 h-8 w-8"
          onClick={playNext}
          disabled={!currentSong}
        >
          <SkipForward className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center gap-2 flex-1 max-w-full sm:max-w-[45%] ${className}`}>
      <div className="flex items-center gap-4 sm:gap-6">
        <Button
          size="icon"
          variant="ghost"
          className="hidden sm:inline-flex hover:text-black text-zinc-600"
        >
          <Shuffle className="h-4 w-4" />
        </Button>

        <Button
          size="icon"
          variant="ghost"
          className="hover:text-black text-zinc-600"
          onClick={playPrevious}
          disabled={!currentSong}
        >
          <SkipBack className="h-4 w-4" />
        </Button>

        <Button
          size="icon"
          className="bg-white hover:bg-white/80 text-black rounded-full h-8 w-8"
          onClick={togglePlay}
          disabled={!currentSong}
        >
          {isPlaying ? (
            <Pause className="h-5 w-5" />
          ) : (
            <Play className="h-5 w-5" />
          )}
        </Button>
        
        <Button
          size="icon"
          variant="ghost"
          className="hover:text-black text-zinc-600"
          onClick={playNext}
          disabled={!currentSong}
        >
          <SkipForward className="h-4 w-4" />
        </Button>
        
        <Button
          size="icon"
          variant="ghost"
          className="hidden sm:inline-flex hover:text-black text-zinc-600"
        >
          <Repeat className="h-4 w-4" />
        </Button>
      </div>

      <div className="hidden sm:flex items-center gap-2 w-full">
        <div className="text-xs text-zinc-600">
          {formatTime(currentTime)}
        </div>
        <Slider
          value={[currentTime]}
          max={duration || 100}
          step={1}
          className="w-full hover:cursor-grab active:cursor-grabbing"
          onValueChange={onSeek}
        />
        <div className="text-xs text-zinc-600">{formatTime(duration)}</div>
      </div>
    </div>
  );
};

export default PlayerControls;
