import {
  Home,
  Search,
  Library,
  Heart,
  Play,
  Pause,
  Skip<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>or<PERSON>,
} from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { PlaybackControls } from "./PlaybackControls";
import { Button } from "@/components/ui/button";

const MobileBottomNav = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { currentSong, isPlaying, togglePlay, playNext, playPrevious } =
    usePlayerStore();

  const navItems = [
    { icon: Home, label: "Home", path: "/" },
    { icon: Search, label: "Search", path: "/search" },
    { icon: Library, label: "Your Library", path: "/library" },
    { icon: Heart, label: "Liked Songs", path: "/liked" },
  ];

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white/98 backdrop-blur-xl border-t border-gray-200/50 z-50 safe-area-bottom">
      {/* Enhanced Mini Player Bar with Controls */}
      {currentSong && (
        <div className="relative">
          {/* Progress Bar */}
          <div className="absolute top-0 left-0 right-0 h-0.5 bg-gray-200">
            {/* <div
              className="h-full bg-primary transition-all duration-300"
              style={{ width: '35%' }} // This would be dynamic based on actual progress
            /> */}
            <PlaybackControls isMobile={true} />
          </div>

          <div className="px-4 py-3 border-b border-gray-200/30">
            <div className="flex items-center gap-3">
              <img
                src={currentSong.imageUrl}
                alt={currentSong.title}
                className="w-10 h-10 rounded-lg object-cover shadow-sm"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate text-gray-900">
                  {currentSong.title}
                </p>
                <p className="text-xs text-primary/70 truncate">
                  {currentSong.artist}
                </p>
              </div>

              <Button
                size="icon"
                variant="ghost"
                className="w-8 h-8 rounded-full bg-primary/10 hover:bg-primary/20 flex items-center justify-center transition-all duration-200 active:scale-95 mobile-button haptic-light"
                onClick={playPrevious}
                disabled={!currentSong}
              >
                <SkipBack className="h-4 w-4 text-primary" />
              </Button>

              {/* Play/Pause Button */}
              <button
                onClick={togglePlay}
                className="w-8 h-8 rounded-full bg-primary/10 hover:bg-primary/20 flex items-center justify-center transition-all duration-200 active:scale-95 mobile-button haptic-light"
                style={{ WebkitTapHighlightColor: "transparent" }}
              >
                {isPlaying ? (
                  <Pause className="w-4 h-4 text-primary" />
                ) : (
                  <Play className="w-4 h-4 text-primary ml-0.5" />
                )}
              </button>
              <Button
                size="icon"
                variant="ghost"
                className="w-8 h-8 rounded-full bg-primary/10 hover:bg-primary/20 flex items-center justify-center transition-all duration-200 active:scale-95 mobile-button haptic-light"
                onClick={playNext}
                disabled={!currentSong}
              >
                <SkipForward className="h-4 w-4 text-primary" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="flex items-center justify-around h-16 px-2">
        {navItems.map((item) => {
          const isActive = location.pathname === item.path;
          const Icon = item.icon;

          return (
            <button
              key={item.path}
              onClick={() => navigate(item.path)}
              className={`flex flex-col items-center justify-center min-w-0 flex-1 py-1 px-2 transition-all duration-200 rounded-lg mobile-button haptic-light no-tap-highlight ${
                isActive
                  ? "text-primary"
                  : "text-gray-600 hover:text-gray-900 active:scale-95"
              }`}
              style={{
                WebkitTapHighlightColor: "transparent",
              }}
            >
              <div
                className={`relative transition-transform duration-200 ${
                  isActive ? "scale-110" : ""
                }`}
              >
                <Icon
                  className={`w-6 h-6 mb-1 transition-colors duration-200 ${
                    isActive ? "text-primary" : "text-gray-600"
                  }`}
                  strokeWidth={isActive ? 2.5 : 2}
                />
                {isActive && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"></div>
                )}
              </div>
              <span
                className={`text-xs font-medium truncate transition-colors duration-200 ${
                  isActive ? "text-primary font-semibold" : "text-gray-600"
                }`}
                style={{
                  fontSize: "10px",
                  lineHeight: "12px",
                }}
              >
                {item.label}
              </span>
            </button>
          );
        })}
      </div>

      {/* Safe area padding for devices with home indicator */}
      <div className="h-safe-area-inset-bottom bg-white/98"></div>
    </div>
  );
};

export default MobileBottomNav;
