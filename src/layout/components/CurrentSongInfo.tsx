import { usePlayerStore } from "@/stores/usePlayerStore";
import { Link } from "react-router-dom";

interface CurrentSongInfoProps {
  className?: string;
}

const CurrentSongInfo = ({ className = "" }: CurrentSongInfoProps) => {
  const { currentSong } = usePlayerStore();

  if (!currentSong) return null;

  return (
    <div className={`flex items-center gap-4 min-w-[180px] w-[30%] ${className}`}>
      <img
        src={currentSong.imageUrl}
        alt={currentSong.title}
        className="w-14 h-14 object-cover rounded-md"
      />
      <div className="flex-1 min-w-0">
        <Link to={`/song/${currentSong._id}`}>
          <div className="font-medium truncate hover:underline cursor-pointer">
            {currentSong.title}
          </div>
        </Link>
        <Link to={`/artist/${currentSong.artistId}`}>
          <div className="text-sm text-muted-foreground truncate hover:underline cursor-pointer">
            {currentSong.artist}
          </div>
        </Link>
      </div>
    </div>
  );
};

export default CurrentSongInfo;
