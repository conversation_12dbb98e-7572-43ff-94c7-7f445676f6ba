import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEffect, useRef, useState } from "react";
import CurrentSongInfo from "./CurrentSongInfo";
import PlayerControls from "./PlayerControls";
import VolumeControls from "./VolumeControls";
import { Slider } from "@/components/ui/slider";

const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

interface PlaybackControlsProps {
  isMobile?: boolean;
}

export const PlaybackControls = ({ isMobile = false }: PlaybackControlsProps) => {
  const { currentSong } = usePlayerStore();
  const [volume, setVolume] = useState(75);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    audioRef.current = document.querySelector("audio");

    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);

    audio.addEventListener("timeupdate", updateTime);
    audio.addEventListener("loadedmetadata", updateDuration);

    const handleEnded = () => {
      usePlayerStore.setState({ isPlaying: false });
    };

    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("timeupdate", updateTime);
      audio.removeEventListener("loadedmetadata", updateDuration);
      audio.removeEventListener("ended", handleEnded);
    };
  }, [currentSong]);

  const handleSeek = (value: number[]) => {
    if (audioRef.current) {
      audioRef.current.currentTime = value[0];
    }
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0]);
    if (audioRef.current) {
      audioRef.current.volume = value[0] / 100;
    }
  };

  if (isMobile) {
    return (
      <div className="flex items-center gap-3 w-full">
        <Slider
          value={[currentTime]}
          max={duration || 100}
          step={1}
          className="w-full hover:cursor-grab active:cursor-grabbing"
          onValueChange={handleSeek}
        />
      </div>
    );
  }

  // Desktop layout
  return (
    <footer className="h-20 sm:h-24 px-4">
      <div className="flex justify-between items-center h-full max-w-[1800px] mx-auto">
        <CurrentSongInfo className="hidden sm:flex" />
        
        <PlayerControls
          currentTime={currentTime}
          duration={duration}
          onSeek={handleSeek}
          formatTime={formatTime}
        />
        
        <VolumeControls
          volume={volume}
          onVolumeChange={handleVolumeChange}
          className="hidden sm:flex"
        />
      </div>
    </footer>
  );
};
