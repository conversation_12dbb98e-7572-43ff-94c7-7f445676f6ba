import { Button } from "@/components/ui/button";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useMusicStore } from "@/stores/useMusicStore";
import { FollowButton } from "@/components/ui/FollowButton";

const RightSidebarArtistInfo = () => {
  const { currentSong } = usePlayerStore();
  const { artists } = useMusicStore();

  if (!currentSong) return null;

  const currentArtist = artists.find(
    (artist) =>
      artist.name === currentSong.artist || artist._id === currentSong.artistId
  );

  if (!currentArtist) return null;

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num.toString();
  };

  return (
    <div className="px-4 py-3 space-y-2">
      <div className="flex items-center gap-3">
        <img
          src={currentArtist.imageUrl}
          alt={currentArtist.name}
          className="w-12 h-12 rounded-full object-cover"
        />
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 leading-tight">
            {currentArtist.name}
          </h3>
          {currentArtist.monthlyListeners && (
            <p className="text-sm text-gray-600 leading-relaxed">
              {formatNumber(currentArtist.monthlyListeners)} monthly listeners
            </p>
          )}
        </div>
      </div>
      {/* <Button
        size="sm"
        variant="outline"
        className="h-8 px-4 rounded-full text-white hover:text-white hover:opacity-90 transition-all duration-200 text-xs font-semibold"
        style={{
          backgroundColor: "#D9AD39",
          border: "none",
          fontSize: "13px",
          fontWeight: "500",
          boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
        }}
      >
        Follow
      </Button> */}
      <FollowButton artistId={currentArtist._id} />
      {currentArtist.about && (
        <p className="text-sm text-gray-600 leading-relaxed mt-2">
          {currentArtist.about}
        </p>
      )}
    </div>
  );
};

export default RightSidebarArtistInfo;
