import { useState, useRef, useEffect } from "react";
import { useSwipeableNavigation, usePullToRefresh } from "@/hooks/useSwipeGestures";
import { But<PERSON> } from "@/components/ui/button";
import { Play, Heart, MoreHorizontal, Shuffle } from "lucide-react";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { Song } from "@/types";
import { cn } from "@/lib/utils";

interface MobilePlaylistViewProps {
  playlist: {
    id: string;
    title: string;
    description?: string;
    imageUrl: string;
    songs: Song[];
    totalDuration?: string;
  };
  onRefresh?: () => Promise<void>;
}

const MobilePlaylistView = ({ playlist, onRefresh }: MobilePlaylistViewProps) => {
  const { currentSong, isPlaying, playAlbum } = usePlayerStore();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);

  // Swipe navigation for playlist images (if multiple)
  const images = [playlist.imageUrl]; // Could be extended for multiple images
  const { attachSwipeListeners, isSwipeActive } = useSwipeableNavigation(
    images,
    currentImageIndex,
    setCurrentImageIndex
  );

  // Pull to refresh functionality
  const { 
    attachPullToRefreshListeners, 
    isRefreshing, 
    pullDistance, 
    refreshProgress 
  } = usePullToRefresh(onRefresh || (() => Promise.resolve()));

  useEffect(() => {
    const cleanup1 = attachSwipeListeners(headerRef.current);
    const cleanup2 = attachPullToRefreshListeners(containerRef.current);

    return () => {
      cleanup1?.();
      cleanup2?.();
    };
  }, [attachSwipeListeners, attachPullToRefreshListeners]);

  const handlePlayPlaylist = () => {
    if (playlist.songs.length > 0) {
      playAlbum(playlist.songs, 0);
    }
  };

  const handleSongPlay = (_song: Song, index: number) => {
    playAlbum(playlist.songs, index);
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  return (
    <div 
      ref={containerRef}
      className="flex flex-col h-full bg-white overflow-hidden"
      style={{ 
        transform: `translateY(${pullDistance * 0.5}px)`,
        transition: pullDistance === 0 ? 'transform 0.3s ease-out' : 'none'
      }}
    >
      {/* Pull to Refresh Indicator */}
      {(pullDistance > 0 || isRefreshing) && (
        <div className="flex justify-center py-2">
          <div 
            className={cn(
              "w-6 h-6 border-2 border-primary border-t-transparent rounded-full",
              isRefreshing ? "animate-spin" : ""
            )}
            style={{
              transform: `rotate(${refreshProgress * 360}deg)`,
              opacity: Math.max(refreshProgress, 0.3)
            }}
          />
        </div>
      )}

      {/* Header Section */}
      <div 
        ref={headerRef}
        className="relative px-4 pt-6 pb-4"
        style={{
          background: 'linear-gradient(180deg, rgba(217, 173, 57, 0.1) 0%, rgba(255, 255, 255, 0) 100%)'
        }}
      >
        {/* Playlist Cover */}
        <div className="flex justify-center mb-6">
          <div 
            className={cn(
              "relative w-64 h-64 rounded-lg overflow-hidden shadow-lg",
              isSwipeActive && "scale-95 transition-transform duration-200"
            )}
          >
            <img
              src={playlist.imageUrl}
              alt={playlist.title}
              className="w-full h-full object-cover"
            />
            
            {/* Gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            
            {/* Play button overlay */}
            <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 active:opacity-100 transition-opacity duration-200">
              <Button
                size="icon"
                className="w-16 h-16 bg-primary hover:bg-primary/90 text-white rounded-full shadow-lg"
                onClick={handlePlayPlaylist}
              >
                <Play className="h-8 w-8 ml-1" />
              </Button>
            </div>
          </div>
        </div>

        {/* Playlist Info */}
        <div className="text-center space-y-2">
          <h1 
            className="text-2xl font-bold text-gray-900"
            style={{
              fontFamily: "SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif",
              fontWeight: "700"
            }}
          >
            {playlist.title}
          </h1>
          
          {playlist.description && (
            <p className="text-sm text-gray-600 max-w-xs mx-auto">
              {playlist.description}
            </p>
          )}
          
          <p className="text-xs text-gray-500">
            {playlist.songs.length} songs
            {playlist.totalDuration && ` • ${playlist.totalDuration}`}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-center gap-4 mt-6">
          <Button
            size="icon"
            variant="ghost"
            className={cn(
              "h-10 w-10 rounded-full",
              isLiked ? "text-red-500" : "text-gray-600"
            )}
            onClick={() => setIsLiked(!isLiked)}
          >
            <Heart className={cn("h-5 w-5", isLiked && "fill-current")} />
          </Button>

          <Button
            className="bg-primary hover:bg-primary/90 text-white px-8 py-3 rounded-full font-semibold"
            onClick={handlePlayPlaylist}
          >
            <Play className="h-4 w-4 mr-2" />
            Play
          </Button>

          <Button
            size="icon"
            variant="ghost"
            className="h-10 w-10 rounded-full text-gray-600"
          >
            <Shuffle className="h-5 w-5" />
          </Button>

          <Button
            size="icon"
            variant="ghost"
            className="h-10 w-10 rounded-full text-gray-600"
          >
            <MoreHorizontal className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Songs List */}
      <div className="flex-1 overflow-y-auto">
        <div className="px-4 py-2">
          {playlist.songs.map((song, index) => {
            const isCurrentSong = currentSong?._id === song._id;
            
            return (
              <div
                key={song._id}
                className={cn(
                  "flex items-center gap-3 py-3 px-2 rounded-lg transition-colors duration-200 active:bg-gray-100",
                  isCurrentSong && "bg-primary/5"
                )}
                onClick={() => handleSongPlay(song, index)}
                style={{ WebkitTapHighlightColor: 'transparent' }}
              >
                {/* Track Number / Playing Indicator */}
                <div className="w-6 flex justify-center">
                  {isCurrentSong && isPlaying ? (
                    <div className="flex items-center gap-0.5">
                      <div className="w-1 h-3 bg-primary rounded-full animate-pulse" />
                      <div className="w-1 h-2 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }} />
                      <div className="w-1 h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.4s' }} />
                    </div>
                  ) : (
                    <span className={cn(
                      "text-sm font-medium",
                      isCurrentSong ? "text-primary" : "text-gray-500"
                    )}>
                      {index + 1}
                    </span>
                  )}
                </div>

                {/* Song Info */}
                <div className="flex-1 min-w-0">
                  <p className={cn(
                    "font-medium truncate",
                    isCurrentSong ? "text-primary" : "text-gray-900"
                  )}>
                    {song.title}
                  </p>
                  <p className="text-sm text-gray-600 truncate">
                    {song.artist}
                  </p>
                </div>

                {/* Duration */}
                <div className="text-xs text-gray-500">
                  {formatDuration(song.duration)}
                </div>

                {/* More Options */}
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-8 w-8 text-gray-400 hover:text-gray-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Handle more options
                  }}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default MobilePlaylistView;