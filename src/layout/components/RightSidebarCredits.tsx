import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useMusicStore } from "@/stores/useMusicStore";
import { ExternalLink } from "lucide-react";
import { FollowButton } from "@/components/ui/FollowButton";

const RightSidebarCredits = () => {
  const { currentSong } = usePlayerStore();
  const { artists } = useMusicStore();

  if (!currentSong) return null;

  const currentArtist = artists.find(
    (artist) =>
      artist.name === currentSong.artist || artist._id === currentSong.artistId
  );

  return (
    <div className="px-4 py-3">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900">Credits</h3>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="link"
              size="sm"
              className="p-0 h-auto text-xs text-gray-500 hover:text-gray-700"
            >
              Show all
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0 bg-white border border-gray-200 shadow-lg" align="end">
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-sm font-semibold text-gray-900">
                  {currentSong.title}
                </h4>
                <Button size="icon" variant="ghost" className="h-8 w-8">
                  <ExternalLink className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-3">
                {/* Main Artist */}
                <div className="flex items-center gap-3">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={currentArtist?.imageUrl} />
                    <AvatarFallback className="bg-gray-100 text-gray-600">
                      {currentSong.artist[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {currentSong.artist}
                    </p>
                    <p className="text-xs text-gray-500">Main Artist</p>
                  </div>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="ml-auto h-7 px-3 rounded-full text-xs text-white hover:opacity-90 transition-all duration-200"
                    style={{
                      backgroundColor: "#D9AD39",
                      border: "none",
                      fontSize: "12px",
                      fontWeight: "500",
                      boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
                    }}
                  >
                    Follow
                  </Button>
                </div>

                {/* Credits */}
                {currentSong.credits?.map((credit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Avatar className="w-10 h-10">
                      <AvatarFallback className="bg-gray-100 text-gray-600">
                        {credit.name[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {credit.name}
                      </p>
                      <p className="text-xs text-gray-500">{credit.role}</p>
                    </div>
                  </div>
                ))}

                {/* Composer */}
                {currentSong.composer && (
                  <div className="flex items-center gap-3">
                    <Avatar className="w-10 h-10">
                      <AvatarFallback className="bg-gray-100 text-gray-600">
                        {currentSong.composer[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {currentSong.composer}
                      </p>
                      <p className="text-xs text-gray-500">Composer</p>
                    </div>
                  </div>
                )}

                {/* Source */}
                {currentSong.source && (
                  <div className="pt-3 border-t border-gray-200">
                    <p className="text-xs text-gray-500">
                      Source: {currentSong.source}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <div className="space-y-2">
        {/* Main Artist */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
          <Avatar className="w-8 h-8">
            <AvatarImage src={currentArtist?.imageUrl} />
            <AvatarFallback className="bg-gray-100 text-gray-600">
              {currentSong.artist[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="text-sm font-medium text-gray-900">
              {currentSong.artist}
            </p>
            <p className="text-xs text-gray-500">Main Artist</p>
          </div>
          </div>
          
          <FollowButton artistId={currentArtist?._id || ""} className="h-6 ml-auto" />
        </div>

        {/* Show first 2 credits */}
        {currentSong.credits?.slice(0, 2).map((credit, index) => (
          <div key={index} className="flex items-center gap-3">
            <Avatar className="w-8 h-8">
              <AvatarFallback className="bg-gray-100 text-gray-600">
                {credit.name[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {credit.name}
              </p>
              <p className="text-xs text-gray-500">{credit.role}</p>
            </div>
          </div>
        ))}

        {/* Composer if different from artist */}
        {currentSong.composer &&
          currentSong.composer !== currentSong.artist && (
            <div className="flex items-center gap-3">
              <Avatar className="w-8 h-8">
                <AvatarFallback className="bg-gray-100 text-gray-600">
                  {currentSong.composer[0]}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {currentSong.composer}
                </p>
                <p className="text-xs text-gray-500">Composer</p>
              </div>
            </div>
          )}
      </div>
    </div>
  );
};

export default RightSidebarCredits;
