import { Button } from "@/components/ui/button";
import { <PERSON>lider } from "@/components/ui/slider";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward, 
  Volume2, 
  VolumeX, 
  Repeat, 
  Repeat1, 
  Shuffle, 
  List,
  Settings
} from "lucide-react";
import { useState, useEffect, useRef } from "react";

const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

interface TabletPlaybackControlsProps {
  volume: number;
  onVolumeChange: (value: number[]) => void;
}

const TabletPlaybackControls = ({ volume, onVolumeChange }: TabletPlaybackControlsProps) => {
  const { currentSong, isPlaying, togglePlay, playNext, playPrevious } = usePlayerStore();
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [repeatMode, setRepeatMode] = useState<'off' | 'all' | 'one'>('off');
  const [isShuffled, setIsShuffled] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [showQueue, setShowQueue] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    audioRef.current = document.querySelector("audio");
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);

    audio.addEventListener("timeupdate", updateTime);
    audio.addEventListener("loadedmetadata", updateDuration);

    return () => {
      audio.removeEventListener("timeupdate", updateTime);
      audio.removeEventListener("loadedmetadata", updateDuration);
    };
  }, [currentSong]);

  const handleSeek = (value: number[]) => {
    if (audioRef.current) {
      audioRef.current.currentTime = value[0];
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    if (audioRef.current) {
      audioRef.current.muted = !isMuted;
    }
  };

  const cycleRepeatMode = () => {
    const modes: ('off' | 'all' | 'one')[] = ['off', 'all', 'one'];
    const currentIndex = modes.indexOf(repeatMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    setRepeatMode(nextMode);
  };

  const toggleShuffle = () => {
    setIsShuffled(!isShuffled);
  };

  const handleSpeedChange = (value: number[]) => {
    const speed = value[0];
    setPlaybackSpeed(speed);
    if (audioRef.current) {
      audioRef.current.playbackRate = speed;
    }
  };

  const getRepeatIcon = () => {
    switch (repeatMode) {
      case 'one':
        return <Repeat1 className="w-5 h-5" />;
      case 'all':
        return <Repeat className="w-5 h-5" />;
      default:
        return <Repeat className="w-5 h-5" />;
    }
  };

  if (!currentSong) return null;

  return (
    <div className="bg-white/98 backdrop-blur-xl border-t border-gray-200/50 px-6 py-4">
      <div className="max-w-6xl mx-auto">
        {/* Progress Bar */}
        <div className="mb-4">
          <Slider
            value={[currentTime]}
            max={duration || 100}
            step={1}
            onValueChange={handleSeek}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          {/* Song Info */}
          <div className="flex items-center gap-4 flex-1 min-w-0 max-w-xs">
            <img
              src={currentSong.imageUrl}
              alt={currentSong.title}
              className="w-14 h-14 rounded-lg object-cover shadow-sm"
            />
            <div className="flex-1 min-w-0">
              <p className="font-semibold text-gray-900 truncate text-sm">
                {currentSong.title}
              </p>
              <p className="text-xs text-gray-600 truncate">
                {currentSong.artist}
              </p>
            </div>
          </div>

          {/* Main Controls */}
          <div className="flex items-center gap-3">
            {/* Secondary Controls */}
            <div className="flex items-center gap-2">
              <Button
                size="icon"
                variant="ghost"
                className={`h-9 w-9 rounded-full transition-all duration-200 active:scale-95 ${
                  isShuffled ? 'text-primary bg-primary/10' : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={toggleShuffle}
                style={{ WebkitTapHighlightColor: 'transparent' }}
              >
                <Shuffle className="w-4 h-4" />
              </Button>

              <Button
                size="icon"
                variant="ghost"
                className={`h-9 w-9 rounded-full transition-all duration-200 active:scale-95 ${
                  repeatMode !== 'off' ? 'text-primary bg-primary/10' : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={cycleRepeatMode}
                style={{ WebkitTapHighlightColor: 'transparent' }}
              >
                {getRepeatIcon()}
              </Button>
            </div>

            {/* Primary Controls */}
            <div className="flex items-center gap-2">
              <Button
                size="icon"
                variant="ghost"
                className="h-10 w-10 rounded-full text-gray-600 hover:text-gray-900 transition-all duration-200 active:scale-95"
                onClick={playPrevious}
                style={{ WebkitTapHighlightColor: 'transparent' }}
              >
                <SkipBack className="w-5 h-5" />
              </Button>

              <Button
                size="icon"
                className="bg-primary hover:bg-primary/90 text-white rounded-full h-12 w-12 transition-all duration-200 active:scale-95 shadow-lg"
                onClick={togglePlay}
                style={{ WebkitTapHighlightColor: 'transparent' }}
              >
                {isPlaying ? (
                  <Pause className="w-6 h-6" />
                ) : (
                  <Play className="w-6 h-6 ml-0.5" />
                )}
              </Button>

              <Button
                size="icon"
                variant="ghost"
                className="h-10 w-10 rounded-full text-gray-600 hover:text-gray-900 transition-all duration-200 active:scale-95"
                onClick={playNext}
                style={{ WebkitTapHighlightColor: 'transparent' }}
              >
                <SkipForward className="w-5 h-5" />
              </Button>
            </div>

            {/* Queue Button */}
            <Button
              size="icon"
              variant="ghost"
              className={`h-9 w-9 rounded-full transition-all duration-200 active:scale-95 ${
                showQueue ? 'text-primary bg-primary/10' : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => setShowQueue(!showQueue)}
              style={{ WebkitTapHighlightColor: 'transparent' }}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>

          {/* Right Side Controls */}
          <div className="flex items-center gap-4 flex-1 justify-end max-w-xs">
            {/* Playback Speed */}
            <div className="flex items-center gap-2 min-w-0">
              <Settings className="w-4 h-4 text-gray-500" />
              <div className="flex items-center gap-2 min-w-0">
                <span className="text-xs text-gray-500 whitespace-nowrap">
                  {playbackSpeed}x
                </span>
                <Slider
                  value={[playbackSpeed]}
                  min={0.5}
                  max={2}
                  step={0.25}
                  onValueChange={handleSpeedChange}
                  className="w-16"
                />
              </div>
            </div>

            {/* Volume Controls */}
            <div className="flex items-center gap-2 min-w-0">
              <Button
                size="icon"
                variant="ghost"
                className="h-8 w-8 rounded-full text-gray-600 hover:text-gray-900 transition-all duration-200 active:scale-95"
                onClick={toggleMute}
                style={{ WebkitTapHighlightColor: 'transparent' }}
              >
                {isMuted || volume === 0 ? (
                  <VolumeX className="w-4 h-4" />
                ) : (
                  <Volume2 className="w-4 h-4" />
                )}
              </Button>
              <Slider
                value={[isMuted ? 0 : volume]}
                max={100}
                step={1}
                onValueChange={onVolumeChange}
                className="w-20"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TabletPlaybackControls;