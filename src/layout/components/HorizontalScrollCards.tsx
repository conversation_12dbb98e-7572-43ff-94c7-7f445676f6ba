import { useRef, useState, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface Card {
  id: string;
  title: string;
  subtitle?: string;
  imageUrl: string;
  onClick?: () => void;
}

interface HorizontalScrollCardsProps {
  title: string;
  cards: Card[];
  cardSize?: 'small' | 'medium' | 'large';
  showArrows?: boolean;
  className?: string;
}

const HorizontalScrollCards = ({ 
  title, 
  cards, 
  cardSize = 'medium',
  showArrows = false,
  className = ""
}: HorizontalScrollCardsProps) => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  const cardSizes = {
    small: "w-32 h-32",
    medium: "w-40 h-40", 
    large: "w-48 h-48"
  };

  const checkScrollButtons = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
    const scrollElement = scrollRef.current;
    if (scrollElement) {
      scrollElement.addEventListener('scroll', checkScrollButtons);
      return () => scrollElement.removeEventListener('scroll', checkScrollButtons);
    }
  }, [cards]);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const scrollAmount = 200;
      scrollRef.current.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className={cn("py-4", className)}>
      {/* Section Header */}
      <div className="flex items-center justify-between px-4 mb-4">
        <h2 
          className="text-xl font-bold text-gray-900"
          style={{
            fontFamily: "SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif",
            fontWeight: "700"
          }}
        >
          {title}
        </h2>
        
        {showArrows && (
          <div className="flex gap-2">
            <Button
              size="icon"
              variant="ghost"
              className={cn(
                "h-8 w-8 rounded-full",
                !canScrollLeft && "opacity-50 cursor-not-allowed"
              )}
              onClick={() => scroll('left')}
              disabled={!canScrollLeft}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              size="icon"
              variant="ghost"
              className={cn(
                "h-8 w-8 rounded-full",
                !canScrollRight && "opacity-50 cursor-not-allowed"
              )}
              onClick={() => scroll('right')}
              disabled={!canScrollRight}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Scrollable Cards Container */}
      <div 
        ref={scrollRef}
        className="flex gap-4 overflow-x-auto px-4 pb-2 scrollbar-hide"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          WebkitOverflowScrolling: 'touch'
        }}
      >
        {cards.map((card, index) => (
          <div
            key={card.id}
            className={cn(
              "flex-shrink-0 cursor-pointer group",
              cardSizes[cardSize]
            )}
            onClick={card.onClick}
            style={{ 
              WebkitTapHighlightColor: 'transparent',
              scrollSnapAlign: 'start'
            }}
          >
            {/* Card Image */}
            <div className="relative w-full h-full mb-2 overflow-hidden rounded-lg shadow-sm group-hover:shadow-md transition-all duration-200">
              <img
                src={card.imageUrl}
                alt={card.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                loading={index < 6 ? "eager" : "lazy"}
              />
              
              {/* Hover Overlay */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200" />
              
              {/* Play Button Overlay (appears on hover) */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center shadow-lg transform scale-90 group-hover:scale-100 transition-transform duration-200">
                  <svg className="w-5 h-5 text-white ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>

            {/* Card Info */}
            <div className="space-y-1">
              <h3 
                className="font-semibold text-sm text-gray-900 truncate"
                style={{
                  fontWeight: "600"
                }}
              >
                {card.title}
              </h3>
              {card.subtitle && (
                <p 
                  className="text-xs text-gray-600 truncate"
                  style={{
                    fontWeight: "400"
                  }}
                >
                  {card.subtitle}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HorizontalScrollCards;