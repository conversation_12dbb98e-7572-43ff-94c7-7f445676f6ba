import { Button } from "@/components/ui/button";
import { usePlayerStore } from "@/stores/usePlayerStore";

const RightSidebarQueue = () => {
  const { queue, currentIndex } = usePlayerStore();

  const nextSong = queue[currentIndex + 1];

  if (!nextSong) return null;

  return (
    <div className="px-4 py-3">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900">
          Next in queue
        </h3>
        <Button
          variant="link"
          size="sm"
          className="p-0 h-auto text-xs text-gray-500 hover:text-gray-700"
        >
          Open queue
        </Button>
      </div>

      <div className="flex items-center gap-3">
        <img
          src={nextSong.imageUrl}
          alt={nextSong.title}
          className="w-12 h-12 rounded-lg object-cover"
        />
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 truncate">
            {nextSong.title}
          </h4>
          <p className="text-xs text-gray-500 truncate">
            {nextSong.artist}
          </p>
        </div>
      </div>
    </div>
  );
};

export default RightSidebarQueue;
