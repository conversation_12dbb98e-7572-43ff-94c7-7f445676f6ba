import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  X,
  Search,
  User,
  Settings,
  LayoutDashboard,
  Bell,
  Users,
  Download,
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Heart,
  ChevronLeft,
  ChevronRight,
  HomeIcon,
  Library,
  ListMusic,
  ListPlus,
  LogOut,
} from "lucide-react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { useSession, signOut } from "@/lib/auth-client";
import { useAuthStore } from "@/stores/useAuthStore";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { usePlaylistStore } from "@/stores/usePlaylistStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { CreatePlaylistDialog } from "@/components/CreatePlaylistDialog";
import { PlaylistOptionsMenu } from "@/components/PlaylistOptionsMenu";
import { cn } from "@/lib/utils";
import PlaylistSkeleton from "@/components/skeletons/PlaylistSkeleton";

interface MobileNavigationDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileNavigationDrawer = ({
  isOpen,
  onClose,
}: MobileNavigationDrawerProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { data: session } = useSession();
  const { isAdmin } = useAuthStore();
  const { currentSong, isPlaying, togglePlay, playNext, playPrevious } =
    usePlayerStore();
  const {
    userPlaylists,
    likedSongsPlaylist,
    fetchUserPlaylists,
    fetchLikedSongsPlaylist,
    isLoading: playlistsLoading,
  } = usePlaylistStore();
  const { fetchUserLikedSongs } = useEngagementStore();
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch playlists when drawer opens
  useEffect(() => {
    if (isOpen && session?.user) {
      fetchUserPlaylists();
      fetchLikedSongsPlaylist();
      fetchUserLikedSongs();
    }
  }, [
    isOpen,
    session?.user,
    fetchUserPlaylists,
    fetchLikedSongsPlaylist,
    fetchUserLikedSongs,
  ]);

  const isActive = (path: string) => location.pathname === path;

  const handleNavigation = (path: string) => {
    navigate(path);
    onClose();
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      onClose();
    }
  };

  const handleSignOut = () => {
    signOut();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 transition-opacity duration-300"
        onClick={onClose}
      />

      {/* Drawer */}
      <div className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-2xl z-[100] transform transition-transform duration-300 ease-out">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Menu</h2>
          <Button
            size="icon"
            variant="ghost"
            onClick={onClose}
            className="h-8 w-8 rounded-full hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        <div className="flex flex-col h-[95%] overflow-y-auto">
          {/* Primary Section */}
          <div className="p-4 space-y-4">
            {/* Enhanced Search */}
            <form onSubmit={handleSearch} className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search songs, artists, albums..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 rounded-full border-gray-200 focus:ring-2 focus:ring-primary/20 focus:border-primary"
              />
            </form>

            {/* Primary Navigation */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-gray-500 mb-3 uppercase tracking-wide">
                Browse Music
              </h3>

              <Link
                to="/"
                onClick={onClose}
                className={cn(
                  "flex items-center gap-3 px-3 py-3 rounded-lg transition-all duration-200 hover:scale-[1.02]",
                  isActive("/")
                    ? "text-white shadow-lg"
                    : "text-gray-700 hover:bg-gray-100"
                )}
                style={{
                  backgroundColor: isActive("/") ? "#D9AD39" : "transparent",
                  fontSize: "16px",
                  fontWeight: isActive("/") ? "600" : "400",
                }}
              >
                <HomeIcon className="w-5 h-5" />
                <span>Home</span>
              </Link>

              <Link
                to="/library"
                onClick={onClose}
                className={cn(
                  "flex items-center gap-3 px-3 py-3 rounded-lg transition-all duration-200 hover:scale-[1.02]",
                  isActive("/library")
                    ? "text-white shadow-lg"
                    : "text-gray-700 hover:bg-gray-100"
                )}
                style={{
                  backgroundColor: isActive("/library")
                    ? "#D9AD39"
                    : "transparent",
                  fontSize: "16px",
                  fontWeight: isActive("/library") ? "600" : "400",
                }}
              >
                <Library className="w-5 h-5" />
                <span>Library</span>
              </Link>
            </div>

            {/* Quick Admin Access */}
            {isAdmin && (
              <div className="pt-2">
                <Button
                  onClick={() => handleNavigation("/admin")}
                  variant="outline"
                  className="w-full justify-start gap-3 h-10"
                >
                  <Settings className="w-4 h-4" />
                  Admin Panel
                </Button>
              </div>
            )}

            {/* User Profile */}
            {session?.user && (
              <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
                {session.user.image ? (
                  <img
                    src={session.user.image}
                    alt="Profile"
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <User className="w-5 h-5 text-primary" />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-gray-900 truncate">
                    {session.user.name || session.user.email}
                  </p>
                  <p className="text-sm text-gray-500">View profile</p>
                </div>
              </div>
            )}
          </div>

          {/* Library Section */}
          {session?.user && (
            <div className="px-4 py-2 border-t border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide">
                  Library
                </h3>
                <CreatePlaylistDialog>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                    title="Create playlist"
                  >
                    <ListPlus className="w-4 h-4" />
                  </Button>
                </CreatePlaylistDialog>
              </div>

              <ScrollArea className="h-64 mb-4">
                <div className="space-y-2">
                  {playlistsLoading && !likedSongsPlaylist ? (
                    <PlaylistSkeleton />
                  ) : (
                    <>
                      {/* Liked Songs - Always show first if user is logged in */}
                      {likedSongsPlaylist && (
                        <Link
                          to="/liked"
                          onClick={onClose}
                          className={cn(
                            "flex items-center gap-3 p-3 rounded-lg transition-all duration-200 hover:scale-[1.02] group cursor-pointer",
                            isActive("/liked")
                              ? "text-white shadow-lg"
                              : "text-gray-700 hover:bg-gray-100"
                          )}
                          style={{
                            backgroundColor: isActive("/liked")
                              ? "#D9AD39"
                              : "transparent",
                          }}
                        >
                          <div className="relative">
                            <div className="w-12 h-12 rounded-md bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center">
                              <Heart className="w-6 h-6 text-white fill-current" />
                            </div>
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-all duration-200" />
                          </div>

                          <div className="flex-1 min-w-0">
                            <p className="font-medium truncate text-base">
                              Liked Songs
                            </p>
                            <p className="text-sm truncate opacity-70">
                              {likedSongsPlaylist.songs.length} liked songs
                            </p>
                          </div>
                        </Link>
                      )}

                      {/* User Playlists */}
                      {userPlaylists.map((playlist) => (
                        <div key={playlist._id} className="group relative">
                          <Link
                            to={`/playlist/${playlist._id}`}
                            onClick={onClose}
                            className={cn(
                              "flex items-center gap-3 p-3 rounded-lg transition-all duration-200 hover:scale-[1.02] cursor-pointer",
                              isActive(`/playlist/${playlist._id}`)
                                ? "text-white shadow-lg"
                                : "text-gray-700 hover:bg-gray-100"
                            )}
                            style={{
                              backgroundColor: isActive(
                                `/playlist/${playlist._id}`
                              )
                                ? "#D9AD39"
                                : "transparent",
                            }}
                          >
                            <div className="relative">
                              <Avatar className="rounded-md w-12 h-12">
                                <AvatarImage
                                  src={
                                    playlist.imageUrl ||
                                    playlist.songs[0]?.imageUrl
                                  }
                                  alt={playlist.name}
                                  className="object-cover"
                                />
                                <AvatarFallback className="bg-gray-200 text-gray-600">
                                  <ListMusic className="w-5 h-5" />
                                </AvatarFallback>
                              </Avatar>
                              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 rounded-lg transition-all duration-200" />
                            </div>

                            <div className="flex-1 min-w-0">
                              <p className="font-medium truncate text-base">
                                {playlist.name}
                              </p>
                              <p className="text-sm truncate opacity-70">
                                Playlist • {playlist.songs.length} songs
                              </p>
                            </div>
                          </Link>

                          {/* Options Menu */}
                          <div className="absolute right-2 top-1/2 -translate-y-1/2">
                            <PlaylistOptionsMenu playlist={playlist} />
                          </div>
                        </div>
                      ))}
                    </>
                  )}
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Secondary Section */}
          <div className="px-4 py-2 border-t border-gray-100">
            {/* <h3 className="text-sm font-medium text-gray-500 mb-3 uppercase tracking-wide">
              Activity
            </h3> */}
            <div className="space-y-2">
              <Button
                onClick={() => handleNavigation("/download")}
                variant="ghost"
                className="w-full justify-start gap-3 h-10"
              >
                <Download className="w-4 h-4" />
                Install App
              </Button>
            </div>
          </div>

          {/* Player Section */}
          {currentSong && (
            <div className="px-4 py-4 border-t border-gray-100 mt-auto">
              <h3 className="text-sm font-medium text-gray-500 mb-3 uppercase tracking-wide">
                Now Playing
              </h3>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <img
                    src={currentSong.imageUrl}
                    alt={currentSong.title}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">
                      {currentSong.title}
                    </p>
                    <p className="text-sm text-gray-500 truncate">
                      {currentSong.artist}
                    </p>
                  </div>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8 rounded-full hover:bg-gray-200"
                  >
                    <Heart className="w-4 h-4" />
                  </Button>
                </div>

                {/* Playback Controls */}
                <div className="flex items-center justify-center gap-4">
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={playPrevious}
                    className="h-8 w-8 rounded-full hover:bg-gray-200"
                  >
                    <SkipBack className="w-4 h-4" />
                  </Button>

                  <Button
                    size="icon"
                    onClick={togglePlay}
                    className="h-10 w-10 rounded-full bg-primary text-white hover:bg-primary/90"
                  >
                    {isPlaying ? (
                      <Pause className="w-5 h-5" />
                    ) : (
                      <Play className="w-5 h-5 ml-0.5" />
                    )}
                  </Button>

                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={playNext}
                    className="h-8 w-8 rounded-full hover:bg-gray-200"
                  >
                    <SkipForward className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Settings & Sign Out */}
          <div className="px-4 py-4 border-t border-gray-100 space-y-2">
            <Button
              onClick={() => handleNavigation("/settings")}
              variant="ghost"
              className="w-full justify-start gap-3 h-10"
            >
              <Settings className="w-4 h-4" />
              Settings
            </Button>

            {session?.user && (
              <Button
                onClick={handleSignOut}
                variant="ghost"
                className="w-full justify-start gap-3 h-10 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <LogOut className="w-4 h-4" />
                Sign Out
              </Button>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileNavigationDrawer;
