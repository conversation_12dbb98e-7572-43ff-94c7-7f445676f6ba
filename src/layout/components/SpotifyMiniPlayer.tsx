import { usePlayerStore } from "@/stores/usePlayerStore";
import { But<PERSON> } from "@/components/ui/button";
import { Pause, Play, SkipFor<PERSON>, Heart } from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface SpotifyMiniPlayerProps {
  onExpand?: () => void;
}

const SpotifyMiniPlayer = ({ onExpand }: SpotifyMiniPlayerProps) => {
  const { currentSong, isPlaying, togglePlay, playNext } = usePlayerStore();
  const [isLiked, setIsLiked] = useState(false);

  if (!currentSong) return null;

  const handlePlayerClick = () => {
    if (onExpand) {
      onExpand();
    }
  };

  return (
    <div className="fixed bottom-16 left-0 right-0 mx-4 mb-2 z-40">
      <div 
        className="bg-white/95 backdrop-blur-xl rounded-lg shadow-lg border border-gray-200/50 overflow-hidden"
        style={{
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05)'
        }}
      >
        {/* Progress Bar */}
        <div className="h-1 bg-gray-200">
          <div 
            className="h-full bg-primary transition-all duration-300"
            style={{ width: '35%' }}
          />
        </div>

        {/* Main Player Content */}
        <div 
          className="flex items-center gap-3 p-3 cursor-pointer active:scale-[0.98] transition-transform duration-150"
          onClick={handlePlayerClick}
          style={{ WebkitTapHighlightColor: 'transparent' }}
        >
          {/* Album Art */}
          <div className="relative">
            <img
              src={currentSong.imageUrl}
              alt={currentSong.title}
              className="w-12 h-12 rounded-lg object-cover shadow-sm"
            />
            {isPlaying && (
              <div className="absolute inset-0 bg-black/20 rounded-lg flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              </div>
            )}
          </div>

          {/* Song Info */}
          <div className="flex-1 min-w-0">
            <p className="text-sm font-semibold text-gray-900 truncate">
              {currentSong.title}
            </p>
            <p className="text-xs text-gray-600 truncate">
              {currentSong.artist}
            </p>
          </div>

          {/* Controls */}
          <div className="flex items-center gap-1">
            <Button
              size="icon"
              variant="ghost"
              className={cn(
                "h-8 w-8 rounded-full transition-colors duration-200 mobile-button haptic-light no-tap-highlight",
                isLiked ? "text-red-500 hover:text-red-600" : "text-gray-600 hover:text-gray-900"
              )}
              onClick={(e) => {
                e.stopPropagation();
                setIsLiked(!isLiked);
              }}
            >
              <Heart 
                className={cn("h-4 w-4", isLiked && "fill-current")} 
              />
            </Button>

            <Button
              size="icon"
              variant="ghost"
              className="h-8 w-8 rounded-full text-gray-900 hover:text-black hover:bg-gray-100 active:scale-95 transition-all duration-150 mobile-button haptic-medium no-tap-highlight"
              onClick={(e) => {
                e.stopPropagation();
                togglePlay();
              }}
            >
              {isPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
            </Button>

            <Button
              size="icon"
              variant="ghost"
              className="h-8 w-8 rounded-full text-gray-600 hover:text-gray-900 hover:bg-gray-100 active:scale-95 transition-all duration-150 mobile-button haptic-light no-tap-highlight"
              onClick={(e) => {
                e.stopPropagation();
                playNext();
              }}
            >
              <SkipForward className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SpotifyMiniPlayer;