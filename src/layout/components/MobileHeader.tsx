import { Menu, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import MobileNavigationDrawer from "./MobileNavigationDrawer";
import { Link, useNavigate } from "react-router-dom";

interface MobileHeaderProps {
  title?: string;
  greeting?: string;
}

const MobileHeader = ({ title, greeting }: MobileHeaderProps) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const navigate = useNavigate();

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 17) return "Good afternoon";
    return "Good evening";
  };

  const displayTitle = title || greeting || getGreeting();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    } else {
      navigate('/search');
    }
  };

  return (
    <>
      <div className="fixed top-0 left-0 right-0 z-40 bg-white/98 backdrop-blur-xl shadow-md border-b border-gray-200/50">
        {/* Safe area for status bar */}
        <div className="h-safe-area-inset-top bg-white/95"></div>

        <div className="flex items-center justify-center gap-3 px-4 py-4">
          {/* Left side - Hamburger Menu */}
          <Button
            size="icon"
            variant="ghost"
            className="h-10 w-10 rounded-full text-gray-700 hover:text-gray-900 hover:bg-gray-100 active:scale-95 transition-all duration-150 mobile-button haptic-light"
            onClick={() => setIsDrawerOpen(true)}
            style={{ WebkitTapHighlightColor: "transparent" }}
          >
            <Menu className="h-6 w-6" strokeWidth={2} />
          </Button>

          {/* Center - Search Input */}
          <div className="flex-1 max-w-md">
            <form onSubmit={handleSearch} className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search songs, artists..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 rounded-full border-gray-200 focus:ring-2 focus:ring-[#D9AD39] focus:border-[#D9AD39] text-sm"
                style={{
                  backgroundColor: "#f8f9fa",
                  fontFamily: "SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif"
                }}
              />
            </form>
          </div>

          {/* Right side - Logo */}
          <Link to="/" className="flex items-center">
            <img
              src="/logo-withoutbd-notscaled.png"
              className="w-20 h-12"
              alt="SLM logo"
            />
          </Link>
        </div>
      </div>

      {/* Navigation Drawer */}
      <MobileNavigationDrawer
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
      />
    </>
  );
};

export default MobileHeader;
