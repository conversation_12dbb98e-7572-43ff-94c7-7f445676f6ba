import { usePlayerStore } from "@/stores/usePlayerStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { useEffect, useRef } from "react";

const AudioPlayer = () => {
	const audioRef = useRef<HTMLAudioElement>(null);
	const prevSongRef = useRef<string | null>(null);

	const { currentSong, isPlaying, playNext } = usePlayerStore();
	const { trackSongPlay } = useEngagementStore();

	// handle play/pause logic
	useEffect(() => {
		const audio = audioRef.current;
		if (!audio) return;

		if (isPlaying) {
			const playPromise = audio.play();
			if (playPromise !== undefined) {
				playPromise.catch(error => {
					console.error("Error playing audio:", error);
				});
			}
		} else {
			audio.pause();
		}
	}, [isPlaying]);

	// handle song ends
	useEffect(() => {
		const audio = audioRef.current;

		const handleEnded = () => {
			playNext();
		};

		audio?.addEventListener("ended", handleEnded);

		return () => audio?.removeEventListener("ended", handleEnded);
	}, [playNext]);

	// handle song changes
	useEffect(() => {
		if (!audioRef.current || !currentSong) return;

		const audio = audioRef.current;

		// check if this is actually a new song
		const isSongChange = prevSongRef.current !== currentSong?.audioUrl;
		if (isSongChange) {
			audio.src = currentSong?.audioUrl;
			// reset the playback position
			audio.currentTime = 0;

			prevSongRef.current = currentSong?.audioUrl;

			// Track the song play when a new song starts
			if (currentSong?._id) {
				trackSongPlay(currentSong._id);
			}

			// Load the audio and play if needed
			audio.load();

			if (isPlaying) {
				// Wait for the audio to be ready before playing
				const handleCanPlay = () => {
					const playPromise = audio.play();
					if (playPromise !== undefined) {
						playPromise.catch(error => {
							console.error("Error playing audio:", error);
						});
					}
					audio.removeEventListener('canplay', handleCanPlay);
				};

				audio.addEventListener('canplay', handleCanPlay);

				// Fallback: try to play immediately in case canplay already fired
				if (audio.readyState >= 3) { // HAVE_FUTURE_DATA
					const playPromise = audio.play();
					if (playPromise !== undefined) {
						playPromise.catch(error => {
							console.error("Error playing audio:", error);
						});
					}
				}
			}
		}
	}, [currentSong, isPlaying, trackSongPlay]);

	return <audio ref={audioRef} />;
};
export default AudioPlayer;
