import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LikeButton } from "@/components/ui/LikeButton";
import { FollowButton } from "@/components/ui/FollowButton";
import { PlaylistSelectionModal } from "@/components/ui/PlaylistSelectionModal";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Download,
  Plus,
  ListMusic,
  User,
  Disc,
  FileText,
  Share,
  ExternalLink,
} from "lucide-react";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useMusicStore } from "@/stores/useMusicStore";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";

const RightSidebarNavbar = () => {
  const { currentSong } = usePlayerStore();
  const { artists } = useMusicStore();
  const { getSongLikeCount, getArtistFollowerCount } = useEngagementStore();
  const [showPlaylistModal, setShowPlaylistModal] = useState(false);

  // Get the current artist from the song
  const currentArtist = currentSong
    ? artists.find(
        (artist) =>
          artist.name === currentSong.artist ||
          artist._id === currentSong.artistId
      )
    : null;

  const handleAddToPlaylist = () => {
    setShowPlaylistModal(true);
  };

  const handleAddToQueue = () => {
    // TODO: Implement add to queue functionality
    console.log("Add to queue clicked");
  };

  const handleGoToArtist = () => {
    // TODO: Navigate to artist page
    console.log("Go to artist clicked");
  };

  const handleGoToAlbum = () => {
    // TODO: Navigate to album page
    console.log("Go to album clicked");
  };

  const handleViewCredits = () => {
    // TODO: Implement view credits functionality
    console.log("View credits clicked");
  };

  const handleShare = () => {
    if (currentSong) {
      // Generate shareable link
      const shareUrl = `${window.location.origin}/track/${currentSong._id}`;
      navigator.clipboard.writeText(shareUrl);
      // TODO: Add toast notification
      console.log("Link copied to clipboard:", shareUrl);
    }
  };

  const handleOpenInApp = () => {
    // TODO: Implement open in app functionality
    console.log("Open in app clicked");
  };

  return (
    <div className="flex items-center justify-between px-4 py-[1.23rem] border-b border-gray-200 bg-white my-5">
      {/* Left Section */}
      <div className="flex items-center gap-3">
        {/* tier Badge */}
        <Link
          to="/admin"
          className={cn(
            "px-4 py-2 rounded-full font-medium transition-all duration-200 hover:scale-105",
            "text-white hover:opacity-90"
          )}
          style={{
            backgroundColor: "#D9AD39",
            fontSize: "14px",
            fontWeight: "500",
            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
          }}
        >
          Upgrade
        </Link>

        {/* Install App Button */}
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-3 rounded-full text-xs font-medium hover:bg-gray-100 transition-colors"
          style={{
            color: "#1d1d1f",
            fontWeight: "500",
          }}
        >
          <Download className="w-4 h-4 mr-1" />
          Install App
        </Button>
      </div>

      {/* Right Section */}
      <div className="flex items-center gap-2">
        {/* More Options Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full hover:bg-gray-100 transition-colors"
            >
              <MoreHorizontal
                className="w-4 h-4"
                style={{ color: "#86868b" }}
              />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-48 mt-2 rounded-xl border-0"
            style={{
              backgroundColor: "#ffffff",
              boxShadow: "0 8px 24px rgba(0,0,0,0.15)",
            }}
          >
            {/* Music Actions */}
            <DropdownMenuItem
              onClick={handleAddToPlaylist}
              className="px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors"
              style={{ color: "#1d1d1f" }}
            >
              <Plus className="w-4 h-4 mr-3" />
              Add to playlist
            </DropdownMenuItem>

            {/* Like Song Button */}
            {currentSong && (
              <div className="px-4 py-3 flex items-center">
                <div onClick={(e) => e.stopPropagation()}>
                  <LikeButton
                    songId={currentSong._id}
                    size="sm"
                    variant="ghost"
                    showCount={true}
                    count={getSongLikeCount(
                      currentSong._id,
                      currentSong.likeCount || 0
                    )}
                  />
                </div>
                <span className="text-sm" style={{ color: "#1d1d1f" }}>
                  Like this song
                </span>
              </div>
            )}

            <DropdownMenuItem
              onClick={handleAddToQueue}
              className="px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors"
              style={{ color: "#1d1d1f" }}
            >
              <ListMusic className="w-4 h-4 mr-3" />
              Add to queue
            </DropdownMenuItem>

            {/* Follow Artist Button */}
            {currentArtist && (
              <div className="w-full">
                {/* <span className="text-sm" style={{ color: "#1d1d1f" }}>
                  Follow {currentArtist.name}
                </span> */}
                <div onClick={(e) => e.stopPropagation()} className="w-full">
                  <FollowButton
                    artistId={currentArtist._id}
                    size="md"
                    variant="ghost"
                    className="w-full rounded-md text-start font-medium"
                    // showCount={true}
                    fullWidth={true}
                    count={getArtistFollowerCount(
                      currentArtist._id,
                      currentArtist.followerCount || 0
                    )}
                  />
                </div>
              </div>
            )}

            <DropdownMenuSeparator
              className="my-2"
              style={{ backgroundColor: "#e8e8ea" }}
            />

            {/* Navigation Actions */}
            <DropdownMenuItem
              // onClick={handleGoToArtist}
              className="px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors"
              style={{ color: "#1d1d1f" }}
            >
              <Link
                to={`/artist/${currentArtist?._id || ""}`}
                className="flex items-center"
              >
                <User className="w-4 h-4 mr-3" />
                Go to artist
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={handleGoToAlbum}
              className="px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors"
              style={{ color: "#1d1d1f" }}
            >
              <Link
                to={`/albums/${currentSong?.albumId || ""}`}
                className="flex items-center"
              >
                <Disc className="w-4 h-4 mr-3" />
                Go to album
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={handleViewCredits}
              className="px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors"
              style={{ color: "#1d1d1f" }}
            >
              <FileText className="w-4 h-4 mr-3" />
              View credits
            </DropdownMenuItem>

            <DropdownMenuSeparator
              className="my-2"
              style={{ backgroundColor: "#e8e8ea" }}
            />

            {/* Share Actions */}
            <DropdownMenuItem
              onClick={handleShare}
              className="px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors"
              style={{ color: "#1d1d1f" }}
            >
              <Share className="w-4 h-4 mr-3" />
              Share
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={handleOpenInApp}
              className="px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors"
              style={{ color: "#1d1d1f" }}
            >
              <ExternalLink className="w-4 h-4 mr-3" />
              Open in app
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Playlist Selection Modal */}
      {currentSong && (
        <PlaylistSelectionModal
          isOpen={showPlaylistModal}
          onClose={() => setShowPlaylistModal(false)}
          songId={currentSong._id}
          songTitle={currentSong.title}
        />
      )}
    </div>
  );
};

export default RightSidebarNavbar;
