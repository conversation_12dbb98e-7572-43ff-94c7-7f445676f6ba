import { Button } from "@/components/ui/button";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { Pause, Play, SkipBack, SkipForward } from "lucide-react";

const MobilePlaybackControls = () => {
  const { currentSong, isPlaying, togglePlay, playNext, playPrevious } = usePlayerStore();

  if (!currentSong) return null;

  return (
    <div className="bg-white rounded-lg shadow-lg p-4 border border-gray-200">
      <div className="flex items-center gap-4">
        {/* Song Info */}
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <img
            src={currentSong.imageUrl}
            alt={currentSong.title}
            className="w-12 h-12 object-cover rounded-md"
          />
          <div className="flex-1 min-w-0">
            <div className="font-medium truncate text-sm">
              {currentSong.title}
            </div>
            <div className="text-xs text-gray-600 truncate">
              {currentSong.artist}
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center gap-2">
          <Button
            size="icon"
            variant="ghost"
            className="h-8 w-8 text-gray-600 hover:text-gray-900"
            onClick={playPrevious}
          >
            <SkipBack className="h-4 w-4" />
          </Button>

          <Button
            size="icon"
            className="bg-gray-900 hover:bg-gray-800 text-white rounded-full h-10 w-10"
            onClick={togglePlay}
          >
            {isPlaying ? (
              <Pause className="h-5 w-5" />
            ) : (
              <Play className="h-5 w-5" />
            )}
          </Button>

          <Button
            size="icon"
            variant="ghost"
            className="h-8 w-8 text-gray-600 hover:text-gray-900"
            onClick={playNext}
          >
            <SkipForward className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MobilePlaybackControls;
