import { ScrollArea } from "@/components/ui/scroll-area";
import { usePlayerStore } from "@/stores/usePlayerStore";
import RightSidebarNavbar from "./RightSidebarNavbar";
import RightSidebarHero from "./RightSidebarHero";
import RightSidebarArtistInfo from "./RightSidebarArtistInfo";
import RightSidebarCredits from "./RightSidebarCredits";
import RightSidebarQueue from "./RightSidebarQueue";

const RightSidebar = () => {
  const { currentSong } = usePlayerStore();

  if (!currentSong) return null;

  return (
    <div className="h-full flex flex-col bg-white w-96 ml-2">
      <RightSidebarNavbar />
      <ScrollArea className="flex-1">
        <div className="space-y-6">
          <RightSidebarHero />
          <RightSidebarArtistInfo />
          <RightSidebarCredits />
          <RightSidebarQueue />
        </div>
      </ScrollArea>
    </div>
  );
};

export default RightSidebar;
