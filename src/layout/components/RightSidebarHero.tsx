import { Button } from "@/components/ui/button";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { useMusicStore } from "@/stores/useMusicStore";
import { Play, Pause, Heart } from "lucide-react";
import { useState } from "react";
import { LikeButton } from "@/components/ui/LikeButton";

const RightSidebarHero = () => {
  const { currentSong, isPlaying, togglePlay } = usePlayerStore();
  const { artists } = useMusicStore();
  const [isLiked, setIsLiked] = useState(false);

  if (!currentSong) return null;

  const currentArtist = artists.find(
    (artist) =>
      artist.name === currentSong.artist || artist._id === currentSong.artistId
  );

  return (
    <div className="relative h-60 overflow-hidden rounded-lg">
      <img
        src={currentSong.imageUrl}
        alt={currentSong.title}
        className="w-full h-full object-cover"
      />
      <div
        className="absolute inset-0"
        style={{
          background: currentArtist?.bgColor
            ? `linear-gradient(to bottom, transparent 0%, ${currentArtist.bgColor}40 70%, ${currentArtist.bgColor}80 100%)`
            : "linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.3) 70%, rgba(0,0,0,0.6) 100%)",
        }}
      />
      <div className="absolute bottom-4 left-4 right-4">
        <div className="flex items-center gap-3">
          <Button
            size="icon"
            variant="ghost"
            className="bg-white/90 hover:bg-white w-12 h-12 rounded-full shadow-lg"
            onClick={togglePlay}
          >
            {isPlaying ? (
              <Pause className="w-6 h-6 text-black" />
            ) : (
              <Play className="w-6 h-6 text-black" />
            )}
          </Button>
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-white truncate leading-tight">
              {currentSong.title}
            </h3>
            <p className="text-sm text-white/80 truncate leading-relaxed">
              {currentSong.artist}
            </p>
          </div>
          <LikeButton
            songId={currentSong._id}
            size="sm"
            variant="ghost"
            className="hover:bg-transparent hover:shadow-none group-hover:opacity-100 transition-opacity duration-200 ml-2 text-white"
          />
        </div>
      </div>
    </div>
  );
};

export default RightSidebarHero;
