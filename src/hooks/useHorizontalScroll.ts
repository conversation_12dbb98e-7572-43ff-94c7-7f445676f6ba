import { useRef, useState, useEffect, useCallback } from 'react';

interface UseHorizontalScrollReturn {
  scrollRef: React.RefObject<HTMLDivElement>;
  canScrollLeft: boolean;
  canScrollRight: boolean;
  scrollLeft: () => void;
  scrollRight: () => void;
  showIndicators: boolean;
}

export const useHorizontalScroll = (): UseHorizontalScrollReturn => {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(true);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const [showIndicators, setShowIndicators] = useState(true);

  const checkScrollability = useCallback(() => {
    const element = scrollRef.current;
    if (!element) return;

    const { scrollLeft, scrollWidth, clientWidth } = element;
    
    setCanScrollLeft(scrollLeft > 0);
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    setShowIndicators(scrollWidth > clientWidth);
  }, []);

  const scrollLeft = useCallback(() => {
    const element = scrollRef.current;
    if (!element) return;

    const cardWidth = 160; // w-40 = 160px
    const gap = 16; // gap-4 = 16px
    const scrollAmount = cardWidth + gap;
    
    element.scrollBy({
      left: -scrollAmount * 2, // Scroll 2 cards at a time
      behavior: 'smooth'
    });
  }, []);

  const scrollRight = useCallback(() => {
    const element = scrollRef.current;
    if (!element) return;

    const cardWidth = 160; // w-40 = 160px
    const gap = 16; // gap-4 = 16px
    const scrollAmount = cardWidth + gap;
    
    element.scrollBy({
      left: scrollAmount * 2, // Scroll 2 cards at a time
      behavior: 'smooth'
    });
  }, []);

  useEffect(() => {
    const element = scrollRef.current;
    if (!element) return;

    // Initial check
    checkScrollability();

    // Check on scroll
    const handleScroll = () => {
      checkScrollability();
    };

    // Check on resize
    const handleResize = () => {
      checkScrollability();
    };

    element.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize);

    // Use ResizeObserver for better detection of content changes
    const resizeObserver = new ResizeObserver(() => {
      checkScrollability();
    });
    resizeObserver.observe(element);

    return () => {
      element.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
      resizeObserver.disconnect();
    };
  }, [checkScrollability]);

  return {
    scrollRef,
    canScrollLeft,
    canScrollRight,
    scrollLeft,
    scrollRight,
    showIndicators
  };
};