import { useRef, useState } from 'react';

interface SwipeGestureOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number;
  preventDefaultTouchmove?: boolean;
}

interface TouchPosition {
  x: number;
  y: number;
}

export const useSwipeGestures = (options: SwipeGestureOptions) => {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    threshold = 50,
    preventDefaultTouchmove = false
  } = options;

  const touchStartPos = useRef<TouchPosition | null>(null);
  const touchEndPos = useRef<TouchPosition | null>(null);
  const [isSwipeActive, setIsSwipeActive] = useState(false);

  const handleTouchStart = (e: TouchEvent) => {
    const touch = e.touches[0];
    touchStartPos.current = {
      x: touch.clientX,
      y: touch.clientY
    };
    setIsSwipeActive(true);
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (preventDefaultTouchmove && isSwipeActive) {
      e.preventDefault();
    }
  };

  const handleTouchEnd = (e: TouchEvent) => {
    if (!touchStartPos.current) return;

    const touch = e.changedTouches[0];
    touchEndPos.current = {
      x: touch.clientX,
      y: touch.clientY
    };

    const deltaX = touchEndPos.current.x - touchStartPos.current.x;
    const deltaY = touchEndPos.current.y - touchStartPos.current.y;

    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    // Determine if it's a horizontal or vertical swipe
    if (absDeltaX > absDeltaY && absDeltaX > threshold) {
      // Horizontal swipe
      if (deltaX > 0) {
        onSwipeRight?.();
      } else {
        onSwipeLeft?.();
      }
    } else if (absDeltaY > absDeltaX && absDeltaY > threshold) {
      // Vertical swipe
      if (deltaY > 0) {
        onSwipeDown?.();
      } else {
        onSwipeUp?.();
      }
    }

    // Reset positions
    touchStartPos.current = null;
    touchEndPos.current = null;
    setIsSwipeActive(false);
  };

  const attachSwipeListeners = (element: HTMLElement | null) => {
    if (!element) return;

    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchmove', handleTouchMove, { passive: !preventDefaultTouchmove });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  };

  return {
    attachSwipeListeners,
    isSwipeActive
  };
};

// Hook for swipeable playlist/album navigation
export const useSwipeableNavigation = (
  items: any[],
  currentIndex: number,
  onIndexChange: (index: number) => void
) => {
  const goToNext = () => {
    if (currentIndex < items.length - 1) {
      onIndexChange(currentIndex + 1);
    }
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      onIndexChange(currentIndex - 1);
    }
  };

  const { attachSwipeListeners, isSwipeActive } = useSwipeGestures({
    onSwipeLeft: goToNext,
    onSwipeRight: goToPrevious,
    threshold: 75,
    preventDefaultTouchmove: true
  });

  return {
    attachSwipeListeners,
    isSwipeActive,
    canGoNext: currentIndex < items.length - 1,
    canGoPrevious: currentIndex > 0,
    goToNext,
    goToPrevious
  };
};

// Hook for pull-to-refresh functionality
export const usePullToRefresh = (onRefresh: () => Promise<void> | void) => {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const startY = useRef<number>(0);
  const currentY = useRef<number>(0);
  const threshold = 80;

  const handleTouchStart = (e: TouchEvent) => {
    startY.current = e.touches[0].clientY;
  };

  const handleTouchMove = (e: TouchEvent) => {
    currentY.current = e.touches[0].clientY;
    const distance = currentY.current - startY.current;

    // Only allow pull down when at the top of the page
    if (window.scrollY === 0 && distance > 0) {
      setPullDistance(Math.min(distance, threshold * 1.5));
      
      if (distance > threshold / 2) {
        e.preventDefault(); // Prevent default scroll behavior
      }
    }
  };

  const handleTouchEnd = async () => {
    if (pullDistance > threshold && !isRefreshing) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
      }
    }
    setPullDistance(0);
  };

  const attachPullToRefreshListeners = (element: HTMLElement | null) => {
    if (!element) return;

    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  };

  return {
    attachPullToRefreshListeners,
    isRefreshing,
    pullDistance,
    refreshProgress: Math.min(pullDistance / threshold, 1)
  };
};