import { Response, NextFunction } from "express";
import type { AuthenticatedRequest } from "../types/index.js";
import { UserLike } from "../models/userLike.model.js";
import { UserFollow } from "../models/userFollow.model.js";
import { PlayHistory } from "../models/playHistory.model.js";
import { Song } from "../models/song.model.js";
import { Artist } from "../models/artist.model.js";
import mongoose from "mongoose";

// Like/Unlike Song
export const toggleSongLike = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { songId } = req.params;
		const userId = req.auth?.user.id;

		if (!userId) {
			res.status(401).json({ message: "Unauthorized" });
			return;
		}

		if (!mongoose.Types.ObjectId.isValid(songId)) {
			res.status(400).json({ message: "Invalid song ID" });
			return;
		}

		// Check if song exists
		const song = await Song.findById(songId);
		if (!song) {
			res.status(404).json({ message: "Song not found" });
			return;
		}

		// Check if user already liked this song
		const existingLike = await UserLike.findOne({
			userId,
			targetId: songId,
			targetType: "song"
		});

		if (existingLike) {
			// Unlike: Remove the like and decrement count
			await UserLike.deleteOne({ _id: existingLike._id });
			await Song.findByIdAndUpdate(songId, {
				$inc: { likeCount: -1 }
			});

			res.json({ 
				message: "Song unliked successfully", 
				isLiked: false,
				likeCount: Math.max(0, (song.likeCount || 0) - 1)
			});
		} else {
			// Like: Create new like and increment count
			await UserLike.create({
				userId,
				targetId: songId,
				targetType: "song"
			});

			await Song.findByIdAndUpdate(songId, {
				$inc: { likeCount: 1 }
			});

			res.json({ 
				message: "Song liked successfully", 
				isLiked: true,
				likeCount: (song.likeCount || 0) + 1
			});
		}
	} catch (error) {
		next(error);
	}
};

// Like/Unlike Artist
export const toggleArtistLike = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { artistId } = req.params;
		const userId = req.auth?.user.id;

		if (!userId) {
			res.status(401).json({ message: "Unauthorized" });
			return;
		}

		if (!mongoose.Types.ObjectId.isValid(artistId)) {
			res.status(400).json({ message: "Invalid artist ID" });
			return;
		}

		// Check if artist exists
		const artist = await Artist.findById(artistId);
		if (!artist) {
			res.status(404).json({ message: "Artist not found" });
			return;
		}

		// Check if user already liked this artist
		const existingLike = await UserLike.findOne({
			userId,
			targetId: artistId,
			targetType: "artist"
		});

		if (existingLike) {
			// Unlike: Remove the like and decrement count
			await UserLike.deleteOne({ _id: existingLike._id });
			await Artist.findByIdAndUpdate(artistId, {
				$inc: { totalLikes: -1 }
			});

			res.json({ 
				message: "Artist unliked successfully", 
				isLiked: false,
				likeCount: Math.max(0, (artist.totalLikes || 0) - 1)
			});
		} else {
			// Like: Create new like and increment count
			await UserLike.create({
				userId,
				targetId: artistId,
				targetType: "artist"
			});

			await Artist.findByIdAndUpdate(artistId, {
				$inc: { totalLikes: 1 }
			});

			res.json({ 
				message: "Artist liked successfully", 
				isLiked: true,
				likeCount: (artist.totalLikes || 0) + 1
			});
		}
	} catch (error) {
		next(error);
	}
};

// Follow/Unfollow Artist
export const toggleArtistFollow = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { artistId } = req.params;
		const userId = req.auth?.user.id;

		if (!userId) {
			res.status(401).json({ message: "Unauthorized" });
			return;
		}

		if (!mongoose.Types.ObjectId.isValid(artistId)) {
			res.status(400).json({ message: "Invalid artist ID" });
			return;
		}

		// Check if artist exists
		const artist = await Artist.findById(artistId);
		if (!artist) {
			res.status(404).json({ message: "Artist not found" });
			return;
		}

		// Check if user already follows this artist
		const existingFollow = await UserFollow.findOne({
			userId,
			artistId
		});

		if (existingFollow) {
			// Unfollow: Remove the follow and decrement count
			await UserFollow.deleteOne({ _id: existingFollow._id });
			await Artist.findByIdAndUpdate(artistId, {
				$inc: { followerCount: -1 }
			});

			res.json({ 
				message: "Artist unfollowed successfully", 
				isFollowing: false,
				followerCount: Math.max(0, (artist.followerCount || 0) - 1)
			});
		} else {
			// Follow: Create new follow and increment count
			await UserFollow.create({
				userId,
				artistId
			});

			await Artist.findByIdAndUpdate(artistId, {
				$inc: { followerCount: 1 }
			});

			res.json({ 
				message: "Artist followed successfully", 
				isFollowing: true,
				followerCount: (artist.followerCount || 0) + 1
			});
		}
	} catch (error) {
		next(error);
	}
};

// Track Song Play
export const trackSongPlay = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { songId } = req.body;
		const userId = req.auth?.user.id;

		if (!userId) {
			res.status(401).json({ message: "Unauthorized" });
			return;
		}

		if (!songId || !mongoose.Types.ObjectId.isValid(songId)) {
			res.status(400).json({ message: "Invalid song ID" });
			return;
		}

		// Check if song exists
		const song = await Song.findById(songId);
		if (!song) {
			res.status(404).json({ message: "Song not found" });
			return;
		}

		// Check for duplicate play within the last 30 seconds (prevent spam)
		const thirtySecondsAgo = new Date(Date.now() - 30 * 1000);
		const recentPlay = await PlayHistory.findOne({
			userId,
			songId,
			playedAt: { $gte: thirtySecondsAgo }
		});

		if (recentPlay) {
			res.json({ 
				message: "Play already tracked recently",
				playCount: song.playCount || 0
			});
			return;
		}

		// Create play history record
		await PlayHistory.create({
			userId,
			songId,
			sessionId: req.auth?.session.id || `session_${Date.now()}`,
			playedAt: new Date()
		});

		// Update song play count and last played timestamp
		const updatedSong = await Song.findByIdAndUpdate(
			songId,
			{
				$inc: { playCount: 1 },
				$set: { lastPlayedAt: new Date() }
			},
			{ new: true }
		);

		// Update artist total plays
		if (song.artistId) {
			await Artist.findByIdAndUpdate(song.artistId, {
				$inc: { totalPlays: 1 },
				$set: { lastPlayedAt: new Date() }
			});
		}

		res.json({ 
			message: "Play tracked successfully",
			playCount: updatedSong?.playCount || 0
		});
	} catch (error) {
		next(error);
	}
};

// Get User's Liked Songs
export const getUserLikedSongs = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	try {
		const userId = req.auth?.user.id;

		if (!userId) {
			res.status(401).json({ message: "Unauthorized" });
			return;
		}

		const likedSongs = await UserLike.find({
			userId,
			targetType: "song"
		}).populate({
			path: "targetId",
			model: "Song"
		}).sort({ createdAt: -1 });

		const songs = likedSongs.map(like => like.targetId).filter(Boolean);

		res.json(songs);
	} catch (error) {
		next(error);
	}
};

// Get User's Followed Artists
export const getUserFollowedArtists = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	try {
		const userId = req.auth?.user.id;

		if (!userId) {
			res.status(401).json({ message: "Unauthorized" });
			return;
		}

		const followedArtists = await UserFollow.find({
			userId
		}).populate({
			path: "artistId",
			model: "Artist"
		}).sort({ createdAt: -1 });

		const artists = followedArtists.map(follow => follow.artistId).filter(Boolean);

		res.json(artists);
	} catch (error) {
		next(error);
	}
};

// Get User's Play History
export const getUserPlayHistory = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	try {
		const userId = req.auth?.user.id;
		const { limit = 50, page = 1 } = req.query;

		if (!userId) {
			res.status(401).json({ message: "Unauthorized" });
			return;
		}

		const skip = (Number(page) - 1) * Number(limit);

		const playHistory = await PlayHistory.find({
			userId
		})
		.populate({
			path: "songId",
			model: "Song"
		})
		.sort({ playedAt: -1 })
		.limit(Number(limit))
		.skip(skip);

		const songs = playHistory.map(play => ({
			...play.songId,
			playedAt: play.playedAt
		})).filter(Boolean);

		res.json(songs);
	} catch (error) {
		next(error);
	}
};

// Check if user likes/follows items
export const checkUserEngagement = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	try {
		const userId = req.auth?.user.id;
		const { songIds, artistIds } = req.body;

		if (!userId) {
			res.status(401).json({ message: "Unauthorized" });
			return;
		}

		const result: {
			likedSongs: string[];
			likedArtists: string[];
			followedArtists: string[];
		} = {
			likedSongs: [],
			likedArtists: [],
			followedArtists: []
		};

		// Check liked songs
		if (songIds && Array.isArray(songIds)) {
			const likedSongs = await UserLike.find({
				userId,
				targetId: { $in: songIds },
				targetType: "song"
			});
			result.likedSongs = likedSongs.map(like => like.targetId.toString());
		}

		// Check liked artists
		if (artistIds && Array.isArray(artistIds)) {
			const likedArtists = await UserLike.find({
				userId,
				targetId: { $in: artistIds },
				targetType: "artist"
			});
			result.likedArtists = likedArtists.map(like => like.targetId.toString());
		}

		// Check followed artists
		if (artistIds && Array.isArray(artistIds)) {
			const followedArtists = await UserFollow.find({
				userId,
				artistId: { $in: artistIds }
			});
			result.followedArtists = followedArtists.map(follow => follow.artistId.toString());
		}

		res.json(result);
	} catch (error) {
		next(error);
	}
};

// Get popular artists based on engagement metrics
export const getPopularArtists = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	try {
		const limit = parseInt(req.query.limit as string) || 10;
		
		// Get popular artists sorted by engagement metrics
		const popularArtists = await Artist.find({})
			.sort({
				followerCount: -1,
				totalLikes: -1,
				totalPlays: -1
			})
			.limit(limit)
			.select('name imageUrl followerCount totalLikes totalPlays lastPlayedAt');

		res.json(popularArtists);
	} catch (error) {
		next(error);
	}
};