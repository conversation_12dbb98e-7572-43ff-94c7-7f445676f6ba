import { Request, Response, NextFunction } from "express";
import { User } from "../models/user.model.js";

// Better Auth handles user creation automatically
// This controller can be used for additional auth-related operations if needed
export const getCurrentUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
	try {
		// Better Auth handles session management
		// This endpoint can be used to get additional user data if needed
		res.status(200).json({ message: "Auth is handled by Better Auth" });
	} catch (error) {
		console.log("Error in auth controller", error);
		next(error);
	}
};
