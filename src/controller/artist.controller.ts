import { Request, Response, NextFunction } from "express";
import { Artist } from "../models/artist.model.js";

export const getAllArtists = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
	try {
		const artists = await Artist.find().sort({ createdAt: -1 });
		res.status(200).json(artists);
	} catch (error) {
		next(error);
	}
};

export const getArtistById = async (req: Request<{ artistId: string }>, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { artistId } = req.params;

		const artist = await Artist.findById(artistId);

		if (!artist) {
			res.status(404).json({ message: "Artist not found" });
			return;
		}

		res.status(200).json(artist);
	} catch (error) {
		next(error);
	}
};
