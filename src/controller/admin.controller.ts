import { Response, NextFunction } from "express";
import mongoose from "mongoose";
import { Song } from "../models/song.model.js";
import { Album } from "../models/album.model.js";
import { Artist } from "../models/artist.model.js";
import cloudinary from "../lib/cloudinary.js";
import type { AuthenticatedRequest } from "../types/index.js";
import { UploadedFile } from "express-fileupload";

// helper function for cloudinary uploads
const uploadToCloudinary = async (file: UploadedFile): Promise<string> => {
	try {
		const result = await cloudinary.uploader.upload(file.tempFilePath, {
			resource_type: "auto",
		});
		return result.secure_url;
	} catch (error) {
		console.log("Error in uploadToCloudinary", error);
		throw new Error("Error uploading to cloudinary");
	}
};

interface CreateSongBody {
	title: string;
	artist: string;
	albumId?: string;
	artistId?: string;
	featuredArtists?: string;
	duration: number;
	releaseDate: string;
	composer?: string;
	producer?: string;
	source?: string;
	credits?: string;
}

interface CreateAlbumBody {
	title: string;
	artist: string;
	artistId?: string;
	releaseYear: number;
	genre: string;
}

interface CreateArtistBody {
	name: string;
	bgColor?: string;
	about?: string;
	monthlyListeners?: string;
	genres?: string;
	socialLinks?: string;
}

export const createSong = async (req: AuthenticatedRequest<{}, any, CreateSongBody>, res: Response, next: NextFunction): Promise<void> => {
	try {
		if (!req.files || !req.files.audioFile || !req.files.imageFile) {
			res.status(400).json({ message: "Please upload all files" });
			return;
		}

		const { title, artist, albumId, artistId, featuredArtists, duration, releaseDate, composer, producer, source, credits } = req.body;
		const audioFile = req.files.audioFile as UploadedFile;
		const imageFile = req.files.imageFile as UploadedFile;

		const audioUrl = await uploadToCloudinary(audioFile);
		const imageUrl = await uploadToCloudinary(imageFile);

		// Parse featured artists if provided
		const featuredArtistsArray = featuredArtists ? featuredArtists.split(',').map(name => name.trim()).filter(name => name.length > 0) : [];

		// Parse credits if provided
		const creditsArray = credits ? JSON.parse(credits) : [];

	// If artistId is not provided, try to find it by artist name
	let finalArtistId = artistId;
	if (!finalArtistId && artist) {
		const existingArtist = await Artist.findOne({ name: artist });
		if (existingArtist) {
			finalArtistId = (existingArtist._id as mongoose.Types.ObjectId).toString();
		}
	}

		const song = new Song({
			title,
			artist,
			audioUrl,
			imageUrl,
			duration,
			releaseDate: releaseDate ? new Date(releaseDate) : new Date(),
			albumId: albumId || undefined,
			artistId: finalArtistId || undefined,
			featuredArtists: featuredArtistsArray,
			composer: composer || undefined,
			producer: producer || undefined,
			source: source || undefined,
			credits: creditsArray,
		});

		await song.save();

		// if song belongs to an album, update the album's songs array
		if (albumId) {
			await Album.findByIdAndUpdate(albumId, {
				$push: { songs: song._id },
			});
		}
		res.status(201).json(song);
	} catch (error) {
		console.log("Error in createSong", error);
		next(error);
	}
};

export const deleteSong = async (req: AuthenticatedRequest<{ id: string }>, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { id } = req.params;

		const song = await Song.findById(id);

		// if song belongs to an album, update the album's songs array
		if (song?.albumId) {
			await Album.findByIdAndUpdate(song.albumId, {
				$pull: { songs: song._id },
			});
		}

		await Song.findByIdAndDelete(id);

		res.status(200).json({ message: "Song deleted successfully" });
	} catch (error) {
		console.log("Error in deleteSong", error);
		next(error);
	}
};

export const createAlbum = async (req: AuthenticatedRequest<{}, any, CreateAlbumBody>, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { title, artist, artistId, releaseYear, genre } = req.body;
		
		if (!req.files || !req.files.imageFile) {
			res.status(400).json({ message: "Please upload an image file" });
			return;
		}

		const imageFile = req.files.imageFile as UploadedFile;
		const imageUrl = await uploadToCloudinary(imageFile);

		const album = new Album({
			title,
			artist,
			imageUrl,
			releaseYear,
			genre,
			artistId: artistId || undefined,
		});

		await album.save();

		res.status(201).json(album);
	} catch (error) {
		console.log("Error in createAlbum", error);
		next(error);
	}
};

export const deleteAlbum = async (req: AuthenticatedRequest<{ id: string }>, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { id } = req.params;
		await Song.deleteMany({ albumId: id });
		await Album.findByIdAndDelete(id);
		res.status(200).json({ message: "Album deleted successfully" });
	} catch (error) {
		console.log("Error in deleteAlbum", error);
		next(error);
	}
};

export const createArtist = async (req: AuthenticatedRequest<{}, any, CreateArtistBody>, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { name, bgColor, about, monthlyListeners, genres, socialLinks } = req.body;
		
		if (!req.files || !req.files.imageFile) {
			res.status(400).json({ message: "Please upload an image file" });
			return;
		}

		const imageFile = req.files.imageFile as UploadedFile;
		const imageUrl = await uploadToCloudinary(imageFile);

		// Parse JSON fields if they exist
		const parsedGenres = genres ? JSON.parse(genres) : [];
		const parsedSocialLinks = socialLinks ? JSON.parse(socialLinks) : {};

		const artist = new Artist({
			name,
			imageUrl,
			bgColor: bgColor || "#1f2937",
			about: about || "",
			monthlyListeners: monthlyListeners ? parseInt(monthlyListeners) : 0,
			genres: parsedGenres,
			socialLinks: parsedSocialLinks,
		});

		await artist.save();

		res.status(201).json(artist);
	} catch (error) {
		console.log("Error in createArtist", error);
		next(error);
	}
};

export const deleteArtist = async (req: AuthenticatedRequest<{ id: string }>, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { id } = req.params;
		
		// Remove artist reference from songs and albums
		await Song.updateMany({ artistId: id }, { $unset: { artistId: 1 } });
		await Album.updateMany({ artistId: id }, { $unset: { artistId: 1 } });
		
		await Artist.findByIdAndDelete(id);
		res.status(200).json({ message: "Artist deleted successfully" });
	} catch (error) {
		console.log("Error in deleteArtist", error);
		next(error);
	}
};

export const updateSong = async (req: AuthenticatedRequest<{ id: string }, any, CreateSongBody>, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { id } = req.params;
		const { title, artist, albumId, artistId, featuredArtists, duration, releaseDate, composer, producer, source, credits } = req.body;

		const song = await Song.findById(id);
		if (!song) {
			res.status(404).json({ message: "Song not found" });
			return;
		}

		// Handle file uploads if provided
		let audioUrl = song.audioUrl;
		let imageUrl = song.imageUrl;

		if (req.files) {
			if (req.files.audioFile) {
				const audioFile = req.files.audioFile as UploadedFile;
				audioUrl = await uploadToCloudinary(audioFile);
			}
			if (req.files.imageFile) {
				const imageFile = req.files.imageFile as UploadedFile;
				imageUrl = await uploadToCloudinary(imageFile);
			}
		}

		// Parse featured artists if provided
		const featuredArtistsArray = featuredArtists ? featuredArtists.split(',').map(name => name.trim()).filter(name => name.length > 0) : song.featuredArtists;

		// Parse credits if provided
		const creditsArray = credits ? JSON.parse(credits) : song.credits;

		// If artistId is not provided or the song doesn't have one, try to find it by artist name
		let finalArtistId = artistId;
		if (!finalArtistId && artist) {
			const existingArtist = await Artist.findOne({ name: artist });
			if (existingArtist) {
				finalArtistId = (existingArtist._id as mongoose.Types.ObjectId).toString();
			}
		}
		// If still no artistId but the song has an existing one, keep it
		if (!finalArtistId && song.artistId) {
			finalArtistId = song.artistId.toString();
		}

		// Update album association if changed
		if (song.albumId !== albumId) {
			// Remove from old album
			if (song.albumId) {
				await Album.findByIdAndUpdate(song.albumId, {
					$pull: { songs: song._id },
				});
			}
			// Add to new album
			if (albumId) {
				await Album.findByIdAndUpdate(albumId, {
					$push: { songs: song._id },
				});
			}
		}

		const updatedSong = await Song.findByIdAndUpdate(
			id,
			{
				title,
				artist,
				audioUrl,
				imageUrl,
				duration,
				releaseDate: releaseDate ? new Date(releaseDate) : song.releaseDate,
				albumId: albumId || undefined,
				artistId: finalArtistId || undefined,
				featuredArtists: featuredArtistsArray,
				composer: composer !== undefined ? composer : song.composer,
				producer: producer !== undefined ? producer : song.producer,
				source: source !== undefined ? source : song.source,
				credits: creditsArray,
			},
			{ new: true }
		);

		res.status(200).json(updatedSong);
	} catch (error) {
		console.log("Error in updateSong", error);
		next(error);
	}
};

export const updateAlbum = async (req: AuthenticatedRequest<{ id: string }, any, CreateAlbumBody>, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { id } = req.params;
		const { title, artist, artistId, releaseYear, genre } = req.body;

		const album = await Album.findById(id);
		if (!album) {
			res.status(404).json({ message: "Album not found" });
			return;
		}

		// Handle image upload if provided
		let imageUrl = album.imageUrl;
		if (req.files && req.files.imageFile) {
			const imageFile = req.files.imageFile as UploadedFile;
			imageUrl = await uploadToCloudinary(imageFile);
		}

		const updatedAlbum = await Album.findByIdAndUpdate(
			id,
			{
				title,
				artist,
				imageUrl,
				releaseYear,
				genre,
				artistId: artistId || undefined,
			},
			{ new: true }
		);

		res.status(200).json(updatedAlbum);
	} catch (error) {
		console.log("Error in updateAlbum", error);
		next(error);
	}
};

export const updateArtist = async (req: AuthenticatedRequest<{ id: string }, any, CreateArtistBody>, res: Response, next: NextFunction): Promise<void> => {
	try {
		const { id } = req.params;
		const { name, bgColor, about, monthlyListeners, genres, socialLinks } = req.body;

		const artist = await Artist.findById(id);
		if (!artist) {
			res.status(404).json({ message: "Artist not found" });
			return;
		}

		// Handle image upload if provided
		let imageUrl = artist.imageUrl;
		if (req.files && req.files.imageFile) {
			const imageFile = req.files.imageFile as UploadedFile;
			imageUrl = await uploadToCloudinary(imageFile);
		}

		// Parse JSON fields if they exist
		const parsedGenres = genres ? JSON.parse(genres) : artist.genres;
		const parsedSocialLinks = socialLinks ? JSON.parse(socialLinks) : artist.socialLinks;

		const updatedArtist = await Artist.findByIdAndUpdate(
			id,
			{
				name,
				imageUrl,
				bgColor: bgColor || artist.bgColor,
				about: about !== undefined ? about : artist.about,
				monthlyListeners: monthlyListeners ? parseInt(monthlyListeners) : artist.monthlyListeners,
				genres: parsedGenres,
				socialLinks: parsedSocialLinks,
			},
			{ new: true }
		);

		res.status(200).json(updatedArtist);
	} catch (error) {
		console.log("Error in updateArtist", error);
		next(error);
	}
};

export const checkAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	res.status(200).json({ admin: true });
};

export const fixMissingArtistIds = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
	try {
		// Find songs that don't have an artistId but do have an artist name
		const songsWithoutArtistId = await Song.find({
			$or: [
				{ artistId: { $exists: false } },
				{ artistId: null },
				{ artistId: "" }
			],
			artist: { $exists: true, $ne: "" }
		});

		let updatedCount = 0;
		let notFoundCount = 0;

		for (const song of songsWithoutArtistId) {
			const artist = await Artist.findOne({ name: song.artist });
			if (artist) {
				await Song.findByIdAndUpdate(song._id, { artistId: artist._id });
				updatedCount++;
			} else {
				notFoundCount++;
			}
		}

		res.status(200).json({
			message: `Fixed artist IDs for ${updatedCount} songs`,
			updatedCount,
			notFoundCount,
			totalProcessed: songsWithoutArtistId.length
		});
	} catch (error) {
		console.log("Error in fixMissingArtistIds", error);
		next(error);
	}
};
