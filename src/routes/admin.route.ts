import { Router, type Router as ExpressRouter } from "express";
import {
  checkAdmin,
  createAlbum,
  createSong,
  deleteAlbum,
  deleteSong,
  createArtist,
  deleteArtist,
  updateSong,
  updateAlbum,
  updateArtist,
  fixMissingArtistIds,
} from "../controller/admin.controller.js";
import { protectRoute, requireAdmin } from "../middleware/auth.middleware.js";

const router: ExpressRouter = Router();

router.use(protectRoute as any, requireAdmin as any);

router.get("/check", checkAdmin as any);
router.post("/fix-artist-ids", fixMissingArtistIds as any);

router.post("/songs", createSong as any);
router.put("/songs/:id", updateSong as any);
router.delete("/songs/:id", deleteSong as any);

router.post("/albums", createAlbum as any);
router.put("/albums/:id", updateAlbum as any);
router.delete("/albums/:id", deleteAlbum as any);

router.post("/artists", createArtist as any);
router.put("/artists/:id", updateArtist as any);
router.delete("/artists/:id", deleteArtist as any);

export default router;
