import { Router, type Router as ExpressRouter } from "express";
import { getAllSongs, getFeaturedSongs, getMadeForYouSongs, getTrendingSongs, getSong } from "../controller/song.controller.js";
import { protectRoute, requireAdmin } from "../middleware/auth.middleware.js";

const router: ExpressRouter = Router();

router.get("/", protectRoute as any, getAllSongs as any);
router.get("/featured", getFeaturedSongs as any);
router.get("/made-for-you", getMadeForYouSongs as any);
router.get("/trending", getTrendingSongs as any);
router.get("/:songId", protectRoute as any, getSong as any); // Assuming this is for fetching a specific song by ID

export default router;
