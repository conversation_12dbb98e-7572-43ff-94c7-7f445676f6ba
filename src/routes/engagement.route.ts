import { Router, type Router as ExpressRouter } from "express";
import {
	toggleSongLike,
	toggleArtistLike,
	toggleArtistFollow,
	trackSongPlay,
	getUserLikedSongs,
	getUserFollowedArtists,
	getUserPlayHistory,
	checkUserEngagement,
	getPopularArtists
} from "../controller/engagement.controller.js";
import { protectRoute } from "../middleware/auth.middleware.js";

const router: ExpressRouter = Router();

// Like/Unlike endpoints
router.post("/songs/:songId/like", protectRoute as any, toggleSongLike as any);
router.post("/artists/:artistId/like", protectRoute as any, toggleArtistLike as any);

// Follow/Unfollow endpoints
router.post("/artists/:artistId/follow", protectRoute as any, toggleArtistFollow as any);

// Play tracking
router.post("/plays/track", protectRoute as any, trackSongPlay as any);

// User engagement data
router.get("/user/liked-songs", protectRoute as any, getUserLikedSongs as any);
router.get("/user/followed-artists", protectRoute as any, getUserFollowedArtists as any);
router.get("/user/play-history", protectRoute as any, getUserPlayHistory as any);

// Bulk check engagement status
router.post("/user/check", protectRoute as any, checkUserEngagement as any);

// Get popular artists (public endpoint)
router.get("/popular-artists", getPopularArtists as any);

export default router;