import { Router } from "express";
import { protectRoute } from "../middleware/auth.middleware";
import {
	getUserPlaylists,
	getLikedSongsPlaylist,
	getPublicPlaylists,
	getPlaylistById,
	createPlaylist,
	updatePlaylist,
	deletePlaylist,
	addSongToPlaylist,
	removeSongFromPlaylist,
	addCollaborator,
	removeCollaborator,
} from "../controller/playlist.controller";

const router: Router = Router();

// Get user's playlists
router.get("/user", protectRoute, getUserPlaylists);

// Get user's liked songs as a playlist
router.get("/liked-songs", protectRoute, getLikedSongsPlaylist);

// Get public playlists
router.get("/public", getPublicPlaylists);

// Get playlist by ID
router.get("/:id", protectRoute, getPlaylistById);

// Create new playlist
router.post("/", protectRoute, createPlaylist);

// Update playlist
router.put("/:id", protectRoute, updatePlaylist);

// Delete playlist
router.delete("/:id", protectRoute, deletePlaylist);

// Add song to playlist
router.post("/:id/songs", protectRoute, addSongToPlaylist);

// Remove song from playlist
router.delete("/:id/songs/:songId", protectRoute, removeSongFromPlaylist);

// Add collaborator to playlist
router.post("/:id/collaborators", protectRoute, addCollaborator);

// Remove collaborator from playlist
router.delete("/:id/collaborators/:collaboratorId", protectRoute, removeCollaborator);

export default router;
