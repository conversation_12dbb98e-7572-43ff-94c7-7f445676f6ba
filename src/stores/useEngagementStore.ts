import { axiosInstance } from "@/lib/axios";
import { 
	Song, 
	Artist, 
	SongWithPlayHistory, 
	EngagementResponse, 
	UserEngagementStatus 
} from "@/types";
import toast from "react-hot-toast";
import { create } from "zustand";

interface EngagementStore {
	// State
	likedSongs: Set<string>;
	likedArtists: Set<string>;
	followedArtists: Set<string>;
	userLikedSongs: Song[];
	userFollowedArtists: Artist[];
	userPlayHistory: SongWithPlayHistory[];
	isLoading: boolean;
	error: string | null;

	// Optimistic update counters
	songLikeCounts: Map<string, number>;
	artistLikeCounts: Map<string, number>;
	artistFollowerCounts: Map<string, number>;
	songPlayCounts: Map<string, number>;
	artistPlayCounts: Map<string, number>;

	// Actions
	toggleSongLike: (songId: string, currentCount?: number) => Promise<void>;
	toggleArtistLike: (artistId: string, currentCount?: number) => Promise<void>;
	toggleArtistFollow: (artistId: string, currentCount?: number) => Promise<void>;
	trackSongPlay: (songId: string, artistId?: string) => Promise<void>;
	fetchUserLikedSongs: () => Promise<void>;
	fetchUserFollowedArtists: () => Promise<void>;
	fetchUserPlayHistory: (limit?: number, page?: number) => Promise<void>;
	checkUserEngagement: (songIds?: string[], artistIds?: string[]) => Promise<void>;
	
	// Utility functions
	isSongLiked: (songId: string) => boolean;
	isArtistLiked: (artistId: string) => boolean;
	isArtistFollowed: (artistId: string) => boolean;
	getSongLikeCount: (songId: string, currentCount?: number) => number;
	getArtistLikeCount: (artistId: string, currentCount?: number) => number;
	getArtistFollowerCount: (artistId: string, currentCount?: number) => number;
	getSongPlayCount: (songId: string, currentCount?: number) => number;
	getArtistPlayCount: (artistId: string, currentCount?: number) => number;
}

export const useEngagementStore = create<EngagementStore>((set, get) => ({
	// Initial state
	likedSongs: new Set(),
	likedArtists: new Set(),
	followedArtists: new Set(),
	userLikedSongs: [],
	userFollowedArtists: [],
	userPlayHistory: [],
	isLoading: false,
	error: null,

	// Optimistic update counters
	songLikeCounts: new Map(),
	artistLikeCounts: new Map(),
	artistFollowerCounts: new Map(),
	songPlayCounts: new Map(),
	artistPlayCounts: new Map(),

	// Toggle song like/unlike
	toggleSongLike: async (songId: string, currentCount = 0) => {
		const { likedSongs } = get();
		const wasLiked = likedSongs.has(songId);
		const optimisticCount = wasLiked ? currentCount - 1 : currentCount + 1;

		// Optimistic update
		set((state) => {
			const newLikedSongs = new Set(state.likedSongs);
			const newSongLikeCounts = new Map(state.songLikeCounts);
			
			if (wasLiked) {
				newLikedSongs.delete(songId);
			} else {
				newLikedSongs.add(songId);
			}
			
			newSongLikeCounts.set(songId, optimisticCount);
			
			return { 
				likedSongs: newLikedSongs,
				songLikeCounts: newSongLikeCounts
			};
		});

		try {
			const response = await axiosInstance.post<EngagementResponse>(
				`/engagement/songs/${songId}/like`
			);
			
			// Update with server response
			set((state) => {
				const newSongLikeCounts = new Map(state.songLikeCounts);
				if (response.data.likeCount !== undefined) {
					newSongLikeCounts.set(songId, response.data.likeCount);
				}
				return { songLikeCounts: newSongLikeCounts };
			});
			
			toast.success(response.data.message);
		} catch (error: any) {
			// Rollback optimistic update
			set((state) => {
				const newLikedSongs = new Set(state.likedSongs);
				const newSongLikeCounts = new Map(state.songLikeCounts);
				
				if (wasLiked) {
					newLikedSongs.add(songId);
				} else {
					newLikedSongs.delete(songId);
				}
				
				newSongLikeCounts.set(songId, currentCount);
				
				return { 
					likedSongs: newLikedSongs,
					songLikeCounts: newSongLikeCounts
				};
			});
			
			toast.error(error.response?.data?.message || "Failed to update like status");
		}
	},

	// Toggle artist like/unlike
	toggleArtistLike: async (artistId: string, currentCount = 0) => {
		const { likedArtists } = get();
		const wasLiked = likedArtists.has(artistId);
		const optimisticCount = wasLiked ? currentCount - 1 : currentCount + 1;

		// Optimistic update
		set((state) => {
			const newLikedArtists = new Set(state.likedArtists);
			const newArtistLikeCounts = new Map(state.artistLikeCounts);
			
			if (wasLiked) {
				newLikedArtists.delete(artistId);
			} else {
				newLikedArtists.add(artistId);
			}
			
			newArtistLikeCounts.set(artistId, optimisticCount);
			
			return { 
				likedArtists: newLikedArtists,
				artistLikeCounts: newArtistLikeCounts
			};
		});

		try {
			const response = await axiosInstance.post<EngagementResponse>(
				`/engagement/artists/${artistId}/like`
			);
			
			// Update with server response
			set((state) => {
				const newArtistLikeCounts = new Map(state.artistLikeCounts);
				if (response.data.likeCount !== undefined) {
					newArtistLikeCounts.set(artistId, response.data.likeCount);
				}
				return { artistLikeCounts: newArtistLikeCounts };
			});
			
			toast.success(response.data.message);
		} catch (error: any) {
			// Rollback optimistic update
			set((state) => {
				const newLikedArtists = new Set(state.likedArtists);
				const newArtistLikeCounts = new Map(state.artistLikeCounts);
				
				if (wasLiked) {
					newLikedArtists.add(artistId);
				} else {
					newLikedArtists.delete(artistId);
				}
				
				newArtistLikeCounts.set(artistId, currentCount);
				
				return { 
					likedArtists: newLikedArtists,
					artistLikeCounts: newArtistLikeCounts
				};
			});
			
			toast.error(error.response?.data?.message || "Failed to update like status");
		}
	},

	// Toggle artist follow/unfollow
	toggleArtistFollow: async (artistId: string, currentCount = 0) => {
		const { followedArtists } = get();
		const wasFollowed = followedArtists.has(artistId);
		const optimisticCount = wasFollowed ? currentCount - 1 : currentCount + 1;

		// Optimistic update
		set((state) => {
			const newFollowedArtists = new Set(state.followedArtists);
			const newArtistFollowerCounts = new Map(state.artistFollowerCounts);
			
			if (wasFollowed) {
				newFollowedArtists.delete(artistId);
			} else {
				newFollowedArtists.add(artistId);
			}
			
			newArtistFollowerCounts.set(artistId, optimisticCount);
			
			return { 
				followedArtists: newFollowedArtists,
				artistFollowerCounts: newArtistFollowerCounts
			};
		});

		try {
			const response = await axiosInstance.post<EngagementResponse>(
				`/engagement/artists/${artistId}/follow`
			);
			
			// Update with server response
			set((state) => {
				const newArtistFollowerCounts = new Map(state.artistFollowerCounts);
				if (response.data.followerCount !== undefined) {
					newArtistFollowerCounts.set(artistId, response.data.followerCount);
				}
				return { artistFollowerCounts: newArtistFollowerCounts };
			});
			
			toast.success(response.data.message);
		} catch (error: any) {
			// Rollback optimistic update
			set((state) => {
				const newFollowedArtists = new Set(state.followedArtists);
				const newArtistFollowerCounts = new Map(state.artistFollowerCounts);
				
				if (wasFollowed) {
					newFollowedArtists.add(artistId);
				} else {
					newFollowedArtists.delete(artistId);
				}
				
				newArtistFollowerCounts.set(artistId, currentCount);
				
				return { 
					followedArtists: newFollowedArtists,
					artistFollowerCounts: newArtistFollowerCounts
				};
			});
			
			toast.error(error.response?.data?.message || "Failed to update follow status");
		}
	},

	// Track song play
	trackSongPlay: async (songId: string, artistId?: string) => {
		// Optimistic update for play counts
		set((state) => {
			const newSongPlayCounts = new Map(state.songPlayCounts);
			const newArtistPlayCounts = new Map(state.artistPlayCounts);
			
			// Increment song play count
			const currentSongCount = newSongPlayCounts.get(songId) || 0;
			newSongPlayCounts.set(songId, currentSongCount + 1);
			
			// Increment artist play count if artistId provided
			if (artistId) {
				const currentArtistCount = newArtistPlayCounts.get(artistId) || 0;
				newArtistPlayCounts.set(artistId, currentArtistCount + 1);
			}
			
			return { 
				songPlayCounts: newSongPlayCounts,
				artistPlayCounts: newArtistPlayCounts
			};
		});

		try {
			const response = await axiosInstance.post<EngagementResponse>("/engagement/plays/track", {
				songId
			});
			
			// Update with server response if provided
			if (response.data.playCount !== undefined) {
				set((state) => {
					const newSongPlayCounts = new Map(state.songPlayCounts);
					newSongPlayCounts.set(songId, response.data.playCount!);
					return { songPlayCounts: newSongPlayCounts };
				});
			}
			
			// Don't show toast for play tracking as it happens frequently
		} catch (error: any) {
			// Rollback optimistic update on error
			set((state) => {
				const newSongPlayCounts = new Map(state.songPlayCounts);
				const newArtistPlayCounts = new Map(state.artistPlayCounts);
				
				const currentSongCount = newSongPlayCounts.get(songId) || 1;
				newSongPlayCounts.set(songId, Math.max(0, currentSongCount - 1));
				
				if (artistId) {
					const currentArtistCount = newArtistPlayCounts.get(artistId) || 1;
					newArtistPlayCounts.set(artistId, Math.max(0, currentArtistCount - 1));
				}
				
				return { 
					songPlayCounts: newSongPlayCounts,
					artistPlayCounts: newArtistPlayCounts
				};
			});
			
			// Silently fail for play tracking to avoid spam
			console.error("Failed to track play:", error);
		}
	},

	// Fetch user's liked songs
	fetchUserLikedSongs: async () => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get<Song[]>("/engagement/user/liked-songs");
			
			set((state) => ({
				userLikedSongs: response.data,
				likedSongs: new Set([
					...state.likedSongs,
					...response.data.map(song => song._id)
				])
			}));
		} catch (error: any) {
			set({ error: error.response?.data?.message || "Failed to fetch liked songs" });
		} finally {
			set({ isLoading: false });
		}
	},

	// Fetch user's followed artists
	fetchUserFollowedArtists: async () => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get<Artist[]>("/engagement/user/followed-artists");
			
			set((state) => ({
				userFollowedArtists: response.data,
				followedArtists: new Set([
					...state.followedArtists,
					...response.data.map(artist => artist._id)
				])
			}));
		} catch (error: any) {
			set({ error: error.response?.data?.message || "Failed to fetch followed artists" });
		} finally {
			set({ isLoading: false });
		}
	},

	// Fetch user's play history
	fetchUserPlayHistory: async (limit = 50, page = 1) => {
		set({ isLoading: true, error: null });
		try {
			const response = await axiosInstance.get<SongWithPlayHistory[]>(
				`/engagement/user/play-history?limit=${limit}&page=${page}`
			);
			
			set({ userPlayHistory: response.data });
		} catch (error: any) {
			set({ error: error.response?.data?.message || "Failed to fetch play history" });
		} finally {
			set({ isLoading: false });
		}
	},

	// Check user engagement status for multiple items
	checkUserEngagement: async (songIds?: string[], artistIds?: string[]) => {
		try {
			const response = await axiosInstance.post<UserEngagementStatus>(
				"/engagement/user/check",
				{ songIds, artistIds }
			);
			
			set((state) => ({
				likedSongs: new Set([
					...state.likedSongs,
					...response.data.likedSongs
				]),
				likedArtists: new Set([
					...state.likedArtists,
					...response.data.likedArtists
				]),
				followedArtists: new Set([
					...state.followedArtists,
					...response.data.followedArtists
				])
			}));
		} catch (error: any) {
			console.error("Failed to check engagement status:", error);
		}
	},

	// Utility functions
	isSongLiked: (songId: string) => {
		return get().likedSongs.has(songId);
	},

	isArtistLiked: (artistId: string) => {
		return get().likedArtists.has(artistId);
	},

	isArtistFollowed: (artistId: string) => {
		return get().followedArtists.has(artistId);
	},

	getSongLikeCount: (songId: string, currentCount = 0) => {
		const { songLikeCounts } = get();
		return songLikeCounts.get(songId) ?? currentCount;
	},

	getArtistLikeCount: (artistId: string, currentCount = 0) => {
		const { artistLikeCounts } = get();
		return artistLikeCounts.get(artistId) ?? currentCount;
	},

	getArtistFollowerCount: (artistId: string, currentCount = 0) => {
		const { artistFollowerCounts } = get();
		return artistFollowerCounts.get(artistId) ?? currentCount;
	},

	getSongPlayCount: (songId: string, currentCount = 0) => {
		const { songPlayCounts } = get();
		return songPlayCounts.get(songId) ?? currentCount;
	},

	getArtistPlayCount: (artistId: string, currentCount = 0) => {
		const { artistPlayCounts } = get();
		return artistPlayCounts.get(artistId) ?? currentCount;
	},
}));

// Export individual functions for easier use
export const trackSongPlay = (songId: string, artistId?: string) => {
	return useEngagementStore.getState().trackSongPlay(songId, artistId);
};