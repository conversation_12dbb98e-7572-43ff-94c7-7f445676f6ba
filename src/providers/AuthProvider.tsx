import { axiosInstance } from "@/lib/axios";
import { useAuthStore } from "@/stores/useAuthStore";
import { useChatStore } from "@/stores/useChatStore";
import { useSession } from "@/lib/auth-client";
import { Loader } from "lucide-react";
import { useEffect, useState } from "react";

const updateApiToken = (token: string | null) => {
	if (token) axiosInstance.defaults.headers.common["Authorization"] = `Bearer ${token}`;
	else delete axiosInstance.defaults.headers.common["Authorization"];
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
	const { data: session, isPending } = useSession();
	const [loading, setLoading] = useState(true);
	const { checkAdminStatus } = useAuthStore();
	const { initSocket, disconnectSocket } = useChatStore();

	useEffect(() => {
		const initAuth = async () => {
			try {
				// Better Auth handles token management automatically
				// You can access the session token if needed
				const token = session?.session?.token;
				updateApiToken(token || null);
				
				if (session?.user?.id) {
					await checkAdminStatus();
					// init socket
					initSocket(session.user.id);
				}
			} catch (error: any) {
				updateApiToken(null);
				console.log("Error in auth provider", error);
			} finally {
				setLoading(false);
			}
		};

		if (!isPending) {
			initAuth();
		}

		// clean up
		return () => disconnectSocket();
	}, [session, isPending, checkAdminStatus, initSocket, disconnectSocket]);

	if (loading || isPending)
		return (
			<div className='h-screen w-full flex items-center justify-center'>
				<Loader className='size-8 text-emerald-500 animate-spin' />
			</div>
		);

	return <>{children}</>;
};
