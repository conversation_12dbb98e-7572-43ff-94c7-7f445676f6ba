import { Response, NextFunction } from "express";
import type { AuthenticatedRequest } from "../types/index.js";
import { getAuth } from "../auth.js";
import { fromNodeHeaders } from "better-auth/node";

export const protectRoute = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Get the auth instance
    const auth = getAuth();
    
    // For Better Auth, we'll check for the session cookie in a simpler way
    const sessionCookie = req.cookies?.session || req.headers.authorization;
    
    if (!sessionCookie) {
      res.status(401).json({ message: "Unauthorized - you must be logged in" });
      return;
    }
    
    // Add basic auth info to request
    const session = await auth.api.getSession({
      headers: await fromNodeHeaders(req.headers),
    });
    
    if (!session) {
      res.status(401).json({ message: "Unauthorized - invalid session" });
      return;
    }
    
    req.auth = session;
    next();
  } catch (error) {
    console.error("Auth middleware error:", error);
    res.status(401).json({ message: "Unauthorized - invalid session" });
  }
};

export const requireAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.auth?.user.id) {
      res.status(401).json({ message: "Unauthorized - you must be logged in" });
      return;
    }

    // Check if user is admin (you can implement your own admin logic here)
    // For now, we'll check if the email contains 'admin' or is a specific admin email
    const userEmail = req.auth.user?.email;
    const isAdmin = userEmail?.includes('admin') || userEmail === process.env.ADMIN_EMAIL;

    console.log("admin: ", isAdmin)
    console.log("userEmail: ", process.env.ADMIN_EMAIL)
    console.log("userEmail: ", userEmail)
    
    if (!isAdmin) {
      res.status(403).json({ message: "Forbidden - admin access required" });
      return;
    }
    
    next();
  } catch (error) {
    res.status(403).json({ message: "Forbidden - admin access required" });
  }
};
