import { useState } from "react";
import { Check, ChevronsUpDown, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useMusicStore } from "@/stores/useMusicStore";

interface AlbumComboboxProps {
	value: string;
	onChange: (value: string) => void;
	onAddAlbum: () => void;
	placeholder?: string;
	emptyText?: string;
}

export function AlbumCombobox({
	value,
	onChange,
	onAddAlbum,
	placeholder = "Select album...",
	emptyText = "No album found.",
}: AlbumComboboxProps) {
	const [open, setOpen] = useState(false);
	const { albums } = useMusicStore();

	const selectedAlbum = albums.find((album) => album._id === value);

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					role="combobox"
					aria-expanded={open}
					className="w-full justify-between bg-zinc-800 border-zinc-700 hover:bg-zinc-700"
				>
					{value === "none" ? "No Album (Single)" : selectedAlbum ? selectedAlbum.title : placeholder}
					<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-full p-0 bg-zinc-800 border-zinc-700">
				<Command className="bg-zinc-800">
					<CommandInput 
						placeholder={`Search albums...`} 
						className="bg-zinc-800 border-zinc-700"
					/>
					<CommandList>
						<CommandEmpty className="py-6 text-center text-sm text-zinc-400">
							{emptyText}
						</CommandEmpty>
						<CommandGroup>
							<CommandItem
								value="none"
								onSelect={() => {
									onChange("none");
									setOpen(false);
								}}
								className="hover:bg-zinc-700"
							>
								<Check
									className={cn(
										"mr-2 h-4 w-4",
										value === "none" ? "opacity-100" : "opacity-0"
									)}
								/>
								No Album (Single)
							</CommandItem>
							{albums.map((album) => (
								<CommandItem
									key={album._id}
									value={album._id}
									onSelect={() => {
										onChange(album._id);
										setOpen(false);
									}}
									className="hover:bg-zinc-700"
								>
									<Check
										className={cn(
											"mr-2 h-4 w-4",
											value === album._id ? "opacity-100" : "opacity-0"
										)}
									/>
									<div className="flex items-center gap-2">
										<img 
											src={album.imageUrl} 
											alt={album.title}
											className="w-6 h-6 rounded object-cover"
										/>
										<div>
											<div className="font-medium">{album.title}</div>
											<div className="text-xs text-zinc-400">{album.artist}</div>
										</div>
									</div>
								</CommandItem>
							))}
						</CommandGroup>
						<div className="border-t border-zinc-700 p-2">
							<Button
								onClick={() => {
									onAddAlbum();
									setOpen(false);
								}}
								variant="ghost"
								className="w-full justify-start text-emerald-400 hover:text-emerald-300 hover:bg-emerald-400/10"
							>
								<Plus className="mr-2 h-4 w-4" />
								Add New Album
							</Button>
						</div>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
