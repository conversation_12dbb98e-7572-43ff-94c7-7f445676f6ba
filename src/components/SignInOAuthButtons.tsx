import { signInWithGoogle } from "@/lib/auth-client";
import { But<PERSON> } from "./ui/button";

const SignInOAuthButtons = () => {
	const signIn = () => {
		signInWithGoogle({ redirectTo: import.meta.env.VITE_FRONTEND_BASE_URL || "http://localhost:3000" });
	};

	return (
		<Button 
			onClick={signIn} 
			variant={"secondary"} 
			className='w-full text-white border-zinc-200 h-11'
		>
			<img src='/google.png' alt='Google' className='size-5' />
			Continue with Google
		</Button>
	);
};
export default SignInOAuthButtons;
