import { Link } from "react-router-dom";
import { Artist } from "@/types";

interface ArtistCardProps {
  artist: Artist;
  className?: string;
}

const ArtistCard = ({ artist, className = "" }: ArtistCardProps) => {
  return (
    <Link
      to={`/artist/${artist._id}`}
      className={`group p-4 rounded-lg hover:bg-gray-50 transition-colors ${className}`}
    >
      <div className="text-center">
        <div className="w-20 h-20 mx-auto mb-3 rounded-full overflow-hidden bg-gray-200">
          <img
            src={artist.imageUrl}
            alt={artist.name}
            className="w-full h-full object-cover"
          />
        </div>
        <p className="font-medium text-gray-900 truncate group-hover:text-[#D9AD39] group-hover:underline">
          {artist.name}
        </p>
        <p className="text-sm text-gray-500">Artist</p>
      </div>
    </Link>
  );
};

export default ArtistCard;
