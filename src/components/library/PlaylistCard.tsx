import { Link } from "react-router-dom";
import { Playlist } from "@/types";
import { ListMusic, Play, Heart } from "lucide-react";

interface PlaylistCardProps {
  playlist: Playlist;
  onPlay: (playlist: Playlist) => void;
  isLikedSongs?: boolean;
  className?: string;
}

const PlaylistCard = ({
  playlist,
  onPlay,
  isLikedSongs = false,
  className = "",
}: PlaylistCardProps) => {
  return (
    <div className={`group ${className}`}>
      <div className="relative cursor-pointer">
        <div className="w-full aspect-square rounded-lg overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center mb-3">
          {isLikedSongs ? (
            <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center relative">
              <Heart className="w-8 h-8 text-white" />
              <div className="absolute inset-0 bg-black/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <Play
                  className="w-6 h-6 text-white"
                  onClick={() => onPlay(playlist)}
                />
              </div>
            </div>
          ) : playlist.imageUrl ||
            (playlist.songs.length > 0 && playlist.songs[0].imageUrl) ? (
            <>
              <img
                src={playlist.imageUrl || playlist.songs[0].imageUrl}
                alt={playlist.name}
                className="w-full h-full object-cover"
              />
              {/* <div className=" inset-0 bg-black/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <Play className="w-6 h-6 text-white" />
              </div> */}
            </>
          ) : (
            <>
              <ListMusic className="w-8 h-8 text-white" />
              {/* <div className="absolute inset-0 bg-black/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <Play className="w-6 h-6 text-white" />
              </div> */}
            </>
          )}
        </div>

        <div className="space-y-1">
          <h3 className="font-medium text-gray-900 truncate group-hover:text-[#D9AD39] group-hover:underline transition-colors">
            <Link to={`/playlist/${playlist._id}`}>
              {!isLikedSongs && playlist.name}
            </Link>
          </h3>
          {!isLikedSongs && (
            <p className="text-sm text-gray-500 truncate">
              {playlist.songs.length} song
              {playlist.songs.length !== 1 ? "s" : ""}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PlaylistCard;
