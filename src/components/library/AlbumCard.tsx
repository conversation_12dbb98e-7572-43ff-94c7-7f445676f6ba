import { Link } from "react-router-dom";
import { Album } from "@/types";

interface AlbumCardProps {
  album: Album;
  className?: string;
}

const AlbumCard = ({ album, className = "" }: AlbumCardProps) => {
  return (
    <div
      className={`group p-4 rounded-lg hover:bg-gray-50 transition-colors ${className}`}
    >
      <div className="text-center">
        <div className="w-20 h-20 mx-auto mb-3 rounded-lg overflow-hidden bg-gray-200">
          <img
            src={album.imageUrl}
            alt={album.title}
            className="w-full h-full object-cover"
          />
        </div>
        <p className="font-medium text-gray-900 truncate hover:text-[#D9AD39] hover:underline cursor-pointer">
          <Link to={`/albums/${album._id}`}>{album.title}</Link>
        </p>
        <p className="text-sm text-muted-foreground hover:text-[#D9AD39] hover:underline cursor-pointer">
          <Link to={`/artist/${album.artistId}`}>{album.artist}</Link>
        </p>
      </div>
    </div>
  );
};

export default AlbumCard;
