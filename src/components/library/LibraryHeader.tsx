import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Filter } from "lucide-react";

interface LibraryHeaderProps {
  activeCategory: string;
  onCategoryChange: (category: string) => void;
  categories: Array<{
    key: string;
    label: string;
    count: number;
  }>;
}

const LibraryHeader = ({
  activeCategory,
  onCategoryChange,
  categories
}: LibraryHeaderProps) => {
  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold text-gray-900">Your Library</h1>

      {/* Category Filters */}
      <div className="flex items-center gap-2">
        <Filter className="w-5 h-5 text-gray-500" />
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Button
              key={category.key}
              variant={activeCategory === category.key ? "default" : "outline"}
              size="sm"
              onClick={() => onCategoryChange(category.key)}
              className={`rounded-full ${
                activeCategory === category.key
                  ? "bg-[#D9AD39] hover:bg-[#D9AD39]/90"
                  : "hover:bg-gray-100"
              }`}
            >
              {category.label}
              {category.count > 0 && (
                <Badge variant="secondary" className="ml-2 text-xs rounded-full">
                  {category.count}
                </Badge>
              )}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LibraryHeader;
