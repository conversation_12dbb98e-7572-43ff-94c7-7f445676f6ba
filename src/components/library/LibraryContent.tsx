import SongList from "@/components/ui/SongList";
import PlaylistCard from "./PlaylistCard";
import ArtistCard from "./ArtistCard";
import AlbumCard from "./AlbumCard";
import { Song, Artist, Album, Playlist } from "@/types";
import { Music, User, Disc3, ListMusic, Heart } from "lucide-react";
import { Link } from "react-router-dom";

interface LibraryContentProps {
  activeCategory: string;
  likedSongsPlaylist: Playlist | null;
  userPlaylists: Playlist[];
  userFollowedArtists: Artist[];
  albums: Album[];
  userLikedSongs: Song[];
  onPlayPlaylist: (playlist: Playlist) => void;
  onPlay: (song: Song) => void;
  onAddToPlaylist: (songId: string) => void;
}

const LibraryContent = ({
  activeCategory,
  likedSongsPlaylist,
  userPlaylists,
  userFollowedArtists,
  albums,
  userLikedSongs,
  onPlayPlaylist,
  onPlay,
  onAddToPlaylist,
}: LibraryContentProps) => {
  const shouldShowSection = (category: string) => {
    return activeCategory === "all" || activeCategory === category;
  };

  return (
    <div className="space-y-8">
      {/* Liked Songs Section */}
      {shouldShowSection("playlists") && likedSongsPlaylist && (
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Heart className="w-6 h-6 text-red-500" />
            Liked Songs
          </h2>

          <div
            className="flex items-center gap-4 p-4 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors group"
          >
            <PlaylistCard
              playlist={likedSongsPlaylist}
              onPlay={onPlayPlaylist}
              isLikedSongs={true}
              className="w-16 h-16"
            />
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-gray-900 group-hover:text-[#D9AD39] group-hover:underline transition-colors">
                <Link to="/liked">Liked Songs</Link>
              </h3>
              <p className="text-sm text-gray-500">
                {likedSongsPlaylist.songs.length} song
                {likedSongsPlaylist.songs.length !== 1 ? "s" : ""}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* User Playlists Section */}
      {shouldShowSection("playlists") && userPlaylists.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <ListMusic className="w-6 h-6" />
            Made by you
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
            {(activeCategory === "all"
              ? userPlaylists.slice(0, 6)
              : userPlaylists
            ).map((playlist) => (
              <PlaylistCard
                key={playlist._id}
                playlist={playlist}
                onPlay={onPlayPlaylist}
              />
            ))}
          </div>
        </div>
      )}

      {/* Recently Played Songs Section */}
      {shouldShowSection("all") && userLikedSongs.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Music className="w-6 h-6" />
            Recently played
          </h2>

          <SongList
            songs={userLikedSongs.slice(0, 10)}
            onPlay={onPlay}
            onAddToPlaylist={onAddToPlaylist}
            showHeader={true}
            maxItems={10}
          />
        </div>
      )}

      {/* Followed Artists Section */}
      {shouldShowSection("artists") && userFollowedArtists.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            {/* <User className="w-6 h-6" /> */}
            Artists you follow
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
            {(activeCategory === "all"
              ? userFollowedArtists.slice(0, 6)
              : userFollowedArtists
            ).map((artist) => (
              <ArtistCard key={artist._id} artist={artist} />
            ))}
          </div>
        </div>
      )}

      {/* Albums Section */}
      {shouldShowSection("albums") && albums.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Disc3 className="w-6 h-6" />
            Albums
          </h2>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
            {(activeCategory === "all" ? albums.slice(0, 6) : albums).map(
              (album) => (
                <AlbumCard key={album._id} album={album} />
              )
            )}
          </div>
        </div>
      )}

      {/* Empty State */}
      {activeCategory !== "all" && (
        <>
          {activeCategory === "playlists" &&
            userPlaylists.length === 0 &&
            !likedSongsPlaylist && (
              <div className="text-center py-12">
                <ListMusic className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-gray-600 mb-2">
                  No playlists yet
                </h2>
                <p className="text-gray-500">
                  Create your first playlist to get started
                </p>
              </div>
            )}

          {activeCategory === "artists" && userFollowedArtists.length === 0 && (
            <div className="text-center py-12">
              <User className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-600 mb-2">
                No followed artists
              </h2>
              <p className="text-gray-500">
                Follow some artists to see them here
              </p>
            </div>
          )}

          {activeCategory === "albums" && albums.length === 0 && (
            <div className="text-center py-12">
              <Disc3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-600 mb-2">
                No albums saved
              </h2>
              <p className="text-gray-500">Save some albums to see them here</p>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default LibraryContent;
