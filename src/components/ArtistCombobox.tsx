import { useState } from "react";
import { Check, ChevronsUpDown, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useMusicStore } from "@/stores/useMusicStore";

interface ArtistComboboxProps {
	value: string;
	onChange: (value: string) => void;
	onAddArtist: () => void;
	placeholder?: string;
	emptyText?: string;
}

export function ArtistCombobox({
	value,
	onChange,
	onAddArtist,
	placeholder = "Select artist...",
	emptyText = "No artist found.",
}: ArtistComboboxProps) {
	const [open, setOpen] = useState(false);
	const { artists } = useMusicStore();

	const selectedArtist = artists.find((artist) => artist._id === value);

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					role="combobox"
					aria-expanded={open}
					className="w-full justify-between bg-zinc-800 border-zinc-700 hover:bg-zinc-700"
				>
					{selectedArtist ? selectedArtist.name : placeholder}
					<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-full p-0 bg-zinc-800 border-zinc-700">
				<Command className="bg-zinc-800">
					<CommandInput placeholder="Search artists..." className="h-9" />
					<CommandList>
						<CommandEmpty>{emptyText}</CommandEmpty>
						<CommandGroup>
							{artists.map((artist) => (
								<CommandItem
									key={artist._id}
									value={artist.name}
									onSelect={() => {
										onChange(artist._id === value ? "" : artist._id);
										setOpen(false);
									}}
									className="hover:bg-zinc-700"
								>
									<div className="flex items-center gap-2">
										<img
											src={artist.imageUrl}
											alt={artist.name}
											className="w-6 h-6 rounded-full object-cover"
										/>
										<span>{artist.name}</span>
									</div>
									<Check
										className={cn(
											"ml-auto h-4 w-4",
											value === artist._id ? "opacity-100" : "opacity-0"
										)}
									/>
								</CommandItem>
							))}
						</CommandGroup>
						<CommandGroup>
							<CommandItem
								onSelect={() => {
									onAddArtist();
									setOpen(false);
								}}
								className="hover:bg-zinc-700 text-emerald-400"
							>
								<Plus className="mr-2 h-4 w-4" />
								Add New Artist
							</CommandItem>
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
