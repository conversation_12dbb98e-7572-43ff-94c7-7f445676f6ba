import { LayoutDashboardIcon, LogOut, User, Search, ChevronLeft, ChevronRight } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useAuthStore } from "@/stores/useAuthStore";
import { cn } from "@/lib/utils";
import { useSession, signOut } from "@/lib/auth-client";
import { Input } from "./ui/input";
import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";

const Topbar = () => {
  const { isAdmin } = useAuthStore();
  const { data: session } = useSession();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    } else {
      navigate('/search');
    }
  };

  return (
    <div
      className="sticky top-0 z-10 backdrop-blur-md border-b"
      style={{
        // backgroundColor: "rgba(245, 245, 247, 0.9)",
        borderBottomColor: "#e8e8ea",
        borderBottomWidth: "1px"
      }}
    >
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left Section: Navigation */}
        <div className="flex items-center gap-4">
          {/* Breadcrumb Navigation */}
          <div className="flex items-center gap-2">
            <button
              onClick={() => navigate(-1)}
              className="p-2 rounded-full hover:bg-black/5 transition-colors"
              style={{ color: "#86868b" }}
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <button
              onClick={() => navigate(1)}
              className="p-2 rounded-full hover:bg-black/5 transition-colors"
              style={{ color: "#86868b" }}
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>

          {/* Brand */}
          <Link to="/" className="flex items-center gap-2 ml-4">
            <img src="/logo-withoutbd-notscaled.png" className="w-24 h-16" alt="SLM logo" />
            {/* <span 
              className="text-lg font-semibold"
              style={{ 
                color: "#1d1d1f",
                fontFamily: "SF Pro Display, -apple-system, BlinkMacSystemFont, sans-serif",
                fontWeight: "600"
              }}
            >
              SLM
            </span> */}
          </Link>
        </div>

        {/* Center Section: Search */}
        <div className="flex-1 max-w-md mx-8">
          <form onSubmit={handleSearch} className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4" style={{ color: "#86868b" }} />
            <Input
              placeholder="Search songs, artists, albums..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-full border-0 focus:ring-2 focus:ring-[#D9AD39]"
              style={{
                backgroundColor: "#ffffff",
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                color: "#1d1d1f",
                fontFamily: "SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif"
              }}
            />
          </form>
        </div>

        {/* Right Section: User Actions */}
        <div className="flex items-center gap-3">
          {isAdmin && (
            <Link
              to="/admin"
              className={cn(
                "px-4 py-2 rounded-full font-medium transition-all duration-200 hover:scale-105",
                "text-white hover:opacity-90"
              )}
              style={{
                backgroundColor: "#D9AD39",
                fontSize: "14px",
                fontWeight: "500",
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
              }}
            >
              <LayoutDashboardIcon className="w-4 h-4 mr-2 inline" />
              Admin Dashboard
            </Link>
          )}

          {!session?.user ? (
            <Link
              to="/sign-in"
              className={cn(
                "px-4 py-2 rounded-full font-medium transition-all duration-200 hover:scale-105",
                "text-black bg-white hover:bg-gray-50 border"
              )}
              style={{
                fontSize: "14px",
                fontWeight: "500",
                boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                borderColor: "#e8e8ea"
              }}
            >
              Sign In
            </Link>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button 
                  className={cn(
                    "px-4 py-2 rounded-full font-medium transition-all duration-200 hover:scale-105",
                    "text-black bg-white hover:bg-gray-50 border flex items-center gap-2"
                  )}
                  style={{
                    fontSize: "14px",
                    fontWeight: "500",
                    boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                    borderColor: "#e8e8ea"
                  }}
                >
                  <User className="w-4 h-4" />
                  {session.user.name || session.user.email}
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="end" 
                className="mt-2 rounded-xl border-0"
                style={{
                  backgroundColor: "#ffffff",
                  boxShadow: "0 8px 24px rgba(0,0,0,0.15)",
                  fontFamily: "SF Pro Text, -apple-system, BlinkMacSystemFont, sans-serif"
                }}
              >
                <DropdownMenuItem 
                  onClick={() => signOut()}
                  className="px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors"
                  style={{ color: "#1d1d1f" }}
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </div>
  );
};

export default Topbar;
