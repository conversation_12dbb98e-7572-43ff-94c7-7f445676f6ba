import { <PERSON>r<PERSON><PERSON>, User<PERSON><PERSON><PERSON>, Check } from "lucide-react";
import { But<PERSON> } from "./button";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { cn } from "@/lib/utils";

interface FollowButtonProps {
	artistId: string;
	size?: "sm" | "md" | "lg";
	variant?: "default" | "secondary" | "outline" | "ghost";
	showCount?: boolean;
	count?: number;
	className?: string;
	fullWidth?: boolean;
}

export function FollowButton({
	artistId,
	size = "md",
	variant = "default",
	showCount = false,
	count = 0,
	className,
	fullWidth = false
}: FollowButtonProps) {
	const {
		toggleArtistFollow,
		isArtistFollowed,
		getArtistFollowerCount
	} = useEngagementStore();

	const isFollowed = isArtistFollowed(artistId);
	const displayCount = getArtistFollowerCount(artistId, count);

	const handleClick = async (e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();
		await toggleArtistFollow(artistId, count);
	};

	const sizeClasses = {
		sm: "h-8 px-3 text-xs",
		md: "h-10 px-4 text-sm",
		lg: "h-12 px-6 text-base"
	};

	const iconSizes = {
		sm: 14,
		md: 16,
		lg: 18
	};

	const buttonVariant = isFollowed ? "secondary" : variant;

	return (
		<div className="flex items-center gap-2">
			<Button
				variant={buttonVariant}
				onClick={handleClick}
				className={cn(
					sizeClasses[size],
					"transition-all text-xs rounded-full px-4 h-8 font-semibold duration-200 hover:scale-105",
					fullWidth && "w-full",
					isFollowed && "border-2 shadow-sm",
					className
				)}
				title={isFollowed ? "Unfollow" : "Follow"}
			>
				{isFollowed && (
					<Check size={iconSizes[size]} className="text-green-500" />
				)}
				{isFollowed ? "Following" : "Follow"}
			</Button>
			
			{showCount && displayCount > 0 && (
				<span className="text-sm text-muted-foreground">
					{displayCount.toLocaleString()} followers
				</span>
			)}
		</div>
	);
}