import { useState, useEffect } from "react";
import { Plus, Search, Music, Users, Lock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { usePlaylistStore } from "@/stores/usePlaylistStore";
import { Playlist } from "@/types";
import { cn } from "@/lib/utils";
import toast from "react-hot-toast";

interface PlaylistSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  songId: string;
  songTitle?: string;
}

export const PlaylistSelectionModal = ({ 
  isOpen, 
  onClose, 
  songId, 
  songTitle 
}: PlaylistSelectionModalProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState("");
  const [newPlaylistDescription, setNewPlaylistDescription] = useState("");
  const [isPublic, setIsPublic] = useState(false);
  const [isCreating, setIsCreating] = useState(false);

  const {
    userPlaylists,
    isLoading,
    fetchUserPlaylists,
    createPlaylist,
    addSongToPlaylist,
  } = usePlaylistStore();

  useEffect(() => {
    if (isOpen) {
      fetchUserPlaylists();
    }
  }, [isOpen, fetchUserPlaylists]);

  const filteredPlaylists = userPlaylists.filter(playlist =>
    playlist.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreatePlaylist = async () => {
    if (!newPlaylistName.trim()) {
      toast.error("Please enter a playlist name");
      return;
    }
    
    setIsCreating(true);
    try {
      const newPlaylist = await createPlaylist({
        name: newPlaylistName.trim(),
        description: newPlaylistDescription.trim() || undefined,
        isPublic,
      });

      if (newPlaylist) {
        // Add the song to the newly created playlist
        await addSongToPlaylist(newPlaylist._id, songId);
        toast.success(`Added "${songTitle}" to "${newPlaylist.name}"`);
        
        // Reset form and close modal
        resetForm();
        onClose();
      }
    } catch (error) {
      console.error("Failed to create playlist:", error);
      toast.error("Failed to create playlist");
    } finally {
      setIsCreating(false);
    }
  };

  const handleAddToPlaylist = async (playlist: Playlist) => {
    try {
      await addSongToPlaylist(playlist._id, songId);
      toast.success(`Added "${songTitle}" to "${playlist.name}"`);
      onClose();
    } catch (error) {
      console.error("Failed to add song to playlist:", error);
      toast.error("Failed to add song to playlist");
    }
  };

  const resetForm = () => {
    setNewPlaylistName("");
    setNewPlaylistDescription("");
    setIsPublic(true);
    setShowCreateForm(false);
    setSearchTerm("");
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Add "{songTitle}" to Playlist
          </DialogTitle>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden">
          <div className="space-y-4 h-full">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search playlists..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Create new playlist button */}
            <Button
              variant="outline"
              onClick={() => setShowCreateForm(!showCreateForm)}
              className="w-full justify-start"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create New Playlist
            </Button>

            {/* Create playlist form */}
            {showCreateForm && (
              <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                <div>
                  <Label htmlFor="playlist-name">Playlist Name</Label>
                  <Input
                    id="playlist-name"
                    placeholder="Enter playlist name"
                    value={newPlaylistName}
                    onChange={(e) => setNewPlaylistName(e.target.value)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="playlist-description">Description (optional)</Label>
                  <Textarea
                    id="playlist-description"
                    placeholder="Enter playlist description"
                    value={newPlaylistDescription}
                    onChange={(e) => setNewPlaylistDescription(e.target.value)}
                    className="min-h-[80px]"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="public-switch"
                    checked={isPublic}
                    onCheckedChange={setIsPublic}
                  />
                  <Label htmlFor="public-switch" className="flex items-center space-x-2">
                    {isPublic ? <Users className="h-4 w-4" /> : <Lock className="h-4 w-4" />}
                    <span>Make playlist public</span>
                  </Label>
                </div>

                <div className="flex space-x-2">
                  <Button
                    onClick={handleCreatePlaylist}
                    disabled={!newPlaylistName.trim() || isCreating}
                    className="flex-1"
                  >
                    {isCreating ? "Creating..." : "Create & Add Song"}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowCreateForm(false)}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            {/* Playlists list */}
            <div className="flex-1 min-h-0">
              <ScrollArea className="h-[300px]">
                {isLoading ? (
                  <div className="flex items-center justify-center h-20">
                    <div className="text-sm text-muted-foreground">Loading playlists...</div>
                  </div>
                ) : filteredPlaylists.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-20 text-muted-foreground">
                    <Music className="h-8 w-8 mb-2" />
                    <div className="text-sm">
                      {searchTerm ? "No playlists found" : "No playlists yet"}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredPlaylists.map((playlist) => (
                      <div
                        key={playlist._id}
                        className={cn(
                          "flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors",
                          "group"
                        )}
                        onClick={() => handleAddToPlaylist(playlist)}
                      >
                        <div className="flex items-center space-x-3">
                          {playlist.imageUrl ? (
                            <img
                              src={playlist.imageUrl}
                              alt={playlist.name}
                              className="w-10 h-10 rounded object-cover"
                            />
                          ) : (
                            <div className="w-10 h-10 rounded bg-muted flex items-center justify-center">
                              <Music className="h-5 w-5 text-muted-foreground" />
                            </div>
                          )}
                          <div>
                            <div className="font-medium">{playlist.name}</div>
                            <div className="text-sm text-muted-foreground flex items-center space-x-2">
                              <span>{playlist.songs.length} song{playlist.songs.length !== 1 ? 's' : ''}</span>
                              {playlist.isPublic ? (
                                <Users className="h-3 w-3" />
                              ) : (
                                <Lock className="h-3 w-3" />
                              )}
                            </div>
                          </div>
                        </div>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          Add
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
