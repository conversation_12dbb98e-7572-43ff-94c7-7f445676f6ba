import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { LikeButton } from "@/components/ui/LikeButton";
import { AlbumName } from "@/components/ui/AlbumName";
import { usePlayerStore } from "@/stores/usePlayerStore";
import { Song } from "@/types";
import { Play, Pause, Clock, Plus } from "lucide-react";

interface SongListProps {
  songs: Song[];
  onPlay: (song: Song) => void;
  onAddToPlaylist?: (songId: string) => void;
  showHeader?: boolean;
  maxItems?: number;
  className?: string;
}

const SongList = ({ 
  songs, 
  onPlay, 
  onAddToPlaylist,
  showHeader = true,
  maxItems,
  className = "" 
}: SongListProps) => {
  const { currentSong, isPlaying } = usePlayerStore();

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const displaySongs = maxItems ? songs.slice(0, maxItems) : songs;

  return (
    <div className={`space-y-1 ${className}`}>
      {/* Header Row */}
      {showHeader && (
        <div className="grid grid-cols-12 gap-4 px-4 py-2 text-sm font-medium text-gray-500 border-b border-gray-200">
          <div className="col-span-1 text-center">#</div>
          <div className="col-span-6">TITLE</div>
          <div className="col-span-3">ALBUM</div>
          <div className="col-span-1 text-center">
            <Clock className="w-4 h-4 mx-auto" />
          </div>
          <div className="col-span-1"></div>
        </div>
      )}

      {/* Songs */}
      {displaySongs.map((song, index) => {
        const isCurrentSong = currentSong?._id === song._id;
        const isCurrentlyPlaying = isCurrentSong && isPlaying;

        return (
          <div
            key={song._id}
            className="grid grid-cols-12 gap-4 px-4 py-3 rounded-lg hover:bg-gray-50 group cursor-pointer transition-colors"
            onClick={() => onPlay(song)}
          >
            <div className="col-span-1 flex items-center justify-center">
              <div className="relative">
                <span
                  className={`text-sm ${
                    isCurrentSong ? "text-[#D9AD39]" : "text-gray-500"
                  } group-hover:hidden`}
                >
                  {index + 1}
                </span>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8 hidden group-hover:flex absolute -top-2 -left-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    onPlay(song);
                  }}
                >
                  {isCurrentlyPlaying ? (
                    <Pause className="w-4 h-4" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="col-span-6 flex items-center gap-3">
              <img
                src={song.imageUrl}
                alt={song.title}
                className="w-10 h-10 rounded object-cover"
              />
              <div className="min-w-0">
                <p
                  className={`font-medium truncate hover:underline hover:text-primary ${
                    isCurrentSong ? "text-[#D9AD39]" : "text-gray-900"
                  }`}
                >
                  <Link to={`/song/${song._id}`}>
                    {song.title}
                  </Link>
                </p>
                <p className="text-sm text-gray-600 truncate hover:underline hover:text-primary">
                  <Link to={`/artist/${song.artistId}`}>
                    {song.artist}
                  </Link>
                </p>
              </div>
            </div>

            <div className="col-span-3 flex items-center">
              <p className="text-sm text-gray-600 truncate">
                <AlbumName albumId={song.albumId} />
              </p>
            </div>

            <div className="col-span-1 flex items-center justify-center">
              <span className="text-sm text-gray-500">
                {formatDuration(song.duration)}
              </span>
            </div>

            <div className="col-span-1 flex items-center justify-center">
              <div className="flex items-center gap-1">
                <LikeButton
                  songId={song._id}
                  size="sm"
                  variant="ghost"
                  count={song.likeCount || 0}
                  className="hidden md:block"
                />
                {onAddToPlaylist && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="w-8 h-8 transition-opacity"
                    onClick={(e) => {
                      e.stopPropagation();
                      onAddToPlaylist(song._id);
                    }}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default SongList;
