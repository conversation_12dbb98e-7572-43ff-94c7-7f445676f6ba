import { Heart } from "lucide-react";
import { But<PERSON> } from "./button";
import { useEngagementStore } from "@/stores/useEngagementStore";
import { cn } from "@/lib/utils";

interface LikeButtonProps {
	songId?: string;
	artistId?: string;
	size?: "sm" | "md" | "lg";
	variant?: "default" | "ghost" | "outline";
	showCount?: boolean;
	count?: number;
	className?: string;
}

export function LikeButton({
	songId,
	artistId,
	size = "md",
	variant = "ghost",
	showCount = false,
	count = 0,
	className
}: LikeButtonProps) {
	const {
		toggleSongLike,
		toggleArtistLike,
		isSongLiked,
		isArtistLiked,
		getSongLikeCount,
		getArtistLikeCount
	} = useEngagementStore();

	if (!songId && !artistId) {
		console.warn("LikeButton: Either songId or artistId must be provided");
		return null;
	}

	const isLiked = songId ? isSongLiked(songId) : isArtistLiked(artistId!);
	const displayCount = songId 
		? getSongLikeCount(songId, count)
		: getArtistLikeCount(artistId!, count);

	const handleClick = async (e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();

		if (songId) {
			await toggleSongLike(songId, count);
		} else if (artistId) {
			await toggleArtistLike(artistId, count);
		}
	};

	const sizeClasses = {
		sm: "h-8 w-8",
		md: "h-10 w-10",
		lg: "h-12 w-12"
	};

	const iconSizes = {
		sm: 16,
		md: 20,
		lg: 24
	};

	return (
		<div className="flex items-center gap-1">
			<Button
				variant={variant}
				size="icon"
				onClick={handleClick}
				className={cn(
					sizeClasses[size],
					"transition-all duration-200 hover:scale-105",
					isLiked && "text-red-500 hover:text-red-600",
					className
				)}
				title={isLiked ? "Unlike" : "Like"}
			>
				<Heart
					size={iconSizes[size]}
					className={cn(
						"transition-all duration-200",
						isLiked && "fill-current"
					)}
				/>
			</Button>
			
			{showCount && displayCount > 0 && (
				<span className="text-sm text-muted-foreground min-w-[20px]">
					{displayCount.toLocaleString()}
				</span>
			)}
		</div>
	);
}