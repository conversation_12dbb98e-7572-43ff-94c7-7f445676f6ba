import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Album } from "@/types";
import { useMusicStore } from "@/stores/useMusicStore";

interface AlbumNameProps {
  albumId: string | null;
  className?: string;
  showLink?: boolean;
}

export const AlbumName = ({ 
  albumId, 
  className = "hover:underline hover:text-primary",
  showLink = true 
}: AlbumNameProps) => {
  const [album, setAlbum] = useState<Album | null>(null);
  const { fetchAlbumByIdNoSet } = useMusicStore();

  useEffect(() => {
    if (albumId) {
      fetchAlbumByIdNoSet(albumId)
        .then(setAlbum)
        .catch(() => setAlbum(null));
    }
  }, [albumId, fetchAlbumByIdNoSet]);

  const albumTitle = album ? album.title : "-";

  if (showLink && albumId && album) {
    return (
      <Link
        to={`/albums/${albumId}`}
        className={className}
      >
        {albumTitle}
      </Link>
    );
  }

  return <span className={className}>{albumTitle}</span>;
};
