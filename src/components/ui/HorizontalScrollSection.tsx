import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useHorizontalScroll } from '@/hooks/useHorizontalScroll';

interface HorizontalScrollSectionProps {
  title: string;
  showAll?: boolean;
  onShowAll?: () => void;
  children: React.ReactNode;
  className?: string;
}

const HorizontalScrollSection: React.FC<HorizontalScrollSectionProps> = ({
  title,
  showAll = true,
  onShowAll,
  children,
  className = ""
}) => {
  const {
    scrollRef,
    canScrollLeft,
    canScrollRight,
    scrollLeft,
    scrollRight,
    showIndicators
  } = useHorizontalScroll();

  return (
    <div className={`mb-8 ${className}`}>
      {/* Header with navigation indicators */}
      <div className="flex items-center justify-between mb-4 px-4">
        <div className="flex items-center gap-3">
          <h2 className="text-xl font-semibold text-gray-900">
            {title}
          </h2>
          
          {/* Navigation Indicators */}
          {showIndicators && (
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className={`w-8 h-8 rounded-full ${
                  canScrollLeft 
                    ? 'text-gray-700 hover:text-gray-900 hover:bg-gray-100' 
                    : 'text-gray-300 cursor-not-allowed'
                }`}
                onClick={scrollLeft}
                disabled={!canScrollLeft}
                aria-label="Scroll left"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                className={`w-8 h-8 rounded-full ${
                  canScrollRight 
                    ? 'text-gray-700 hover:text-gray-900 hover:bg-gray-100' 
                    : 'text-gray-300 cursor-not-allowed'
                }`}
                onClick={scrollRight}
                disabled={!canScrollRight}
                aria-label="Scroll right"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
        
        {showAll && (
          <Button
            variant="link"
            className="text-sm text-gray-600 hover:text-gray-900 p-0 h-auto"
            onClick={onShowAll}
          >
            Show all
          </Button>
        )}
      </div>

      {/* Scrollable Content */}
      <div
        ref={scrollRef}
        className="flex overflow-x-auto gap-4 px-4 pb-2 scrollbar-hide"
      >
        {children}
      </div>
      
      {/* Scroll Indicators (dots) - Context-aware visibility */}
      {showIndicators && (
        <div className="flex justify-center mt-3 gap-1">
          <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-200 ${
            canScrollLeft ? 'bg-gray-400' : 'bg-gray-600'
          }`} />
          <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-200 ${
            !canScrollLeft && !canScrollRight ? 'bg-gray-600' : 'bg-gray-400'
          }`} />
          <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-200 ${
            canScrollRight ? 'bg-gray-400' : 'bg-gray-600'
          }`} />
        </div>
      )}
    </div>
  );
};

export default HorizontalScrollSection;