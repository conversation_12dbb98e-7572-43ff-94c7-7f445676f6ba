import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search } from "lucide-react";

interface SearchHeaderProps {
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  onSearch: (e: React.FormEvent) => void;
  activeCategory: string;
  onCategoryChange: (category: string) => void;
  categories: Array<{
    key: string;
    label: string;
    count: number;
  }>;
}

const SearchHeader = ({
  searchQuery,
  onSearchQueryChange,
  onSearch,
  activeCategory,
  onCategoryChange,
  categories
}: SearchHeaderProps) => {
  return (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold text-gray-900">Search</h1>

      {/* Search Input */}
      <form onSubmit={onSearch} className="relative max-w-2xl">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
        <Input
          placeholder="Search songs, artists, albums, playlists..."
          value={searchQuery}
          onChange={(e) => onSearchQueryChange(e.target.value)}
          className="pl-12 pr-4 py-3 text-lg rounded-full border-gray-200 focus:ring-2 focus:ring-[#D9AD39] focus:border-[#D9AD39]"
        />
      </form>

      {/* Category Filters */}
      {searchQuery.trim() && (
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Button
              key={category.key}
              variant={activeCategory === category.key ? "default" : "outline"}
              size="sm"
              onClick={() => onCategoryChange(category.key)}
              className={`rounded-full ${
                activeCategory === category.key
                  ? "bg-[#D9AD39] hover:bg-[#D9AD39]/90"
                  : "hover:bg-gray-100"
              }`}
            >
              {category.label}
              {category.count > 0 && (
                <Badge variant="secondary" className="ml-2 text-xs rounded-full">
                  {category.count}
                </Badge>
              )}
            </Button>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchHeader;
