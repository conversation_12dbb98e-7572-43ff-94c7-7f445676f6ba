import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import SongList from "@/components/ui/SongList";
import { Song, Artist, Album, Playlist } from "@/types";
import { Music, User, Disc3, ListMusic, Search } from "lucide-react";

interface SearchResultsProps {
  results: {
    songs: Song[];
    artists: Artist[];
    albums: Album[];
    playlists: Playlist[];
  };
  activeCategory: string;
  searchQuery: string;
  onPlay: (song: Song) => void;
  onAddToPlaylist: (songId: string) => void;
  onCategoryChange: (category: string) => void;
}

const SearchResults = ({
  results,
  activeCategory,
  searchQuery,
  onPlay,
  onAddToPlaylist,
  onCategoryChange,
}: SearchResultsProps) => {
  const hasResults = Object.values(results).some((arr) => arr.length > 0);

  if (!searchQuery.trim()) {
    return (
      <div className="text-center py-12">
        <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-600 mb-2">
          Start searching for music
        </h2>
        <p className="text-gray-500">
          Find your favorite songs, artists, albums, and playlists
        </p>
      </div>
    );
  }

  if (!hasResults) {
    return (
      <div className="text-center py-12">
        <Music className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-gray-600 mb-2">
          No results found
        </h2>
        <p className="text-gray-500">Try searching with different keywords</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Songs Results */}
      {results.songs.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Music className="w-6 h-6" />
              Songs
            </h2>
            {activeCategory === "all" && results.songs.length > 5 && (
              <Button
                variant="ghost"
                onClick={() => onCategoryChange("songs")}
                className="text-[#D9AD39] hover:text-[#D9AD39]/80"
              >
                Show all
              </Button>
            )}
          </div>

          <SongList
            songs={
              activeCategory === "all"
                ? results.songs.slice(0, 5)
                : results.songs
            }
            onPlay={onPlay}
            onAddToPlaylist={onAddToPlaylist}
            showHeader={true}
          />
        </div>
      )}

      {/* Artists Results */}
      {results.artists.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              {/* <User className="w-6 h-6" /> */}
              Artists
            </h2>
            {activeCategory === "all" && results.artists.length > 6 && (
              <Button
                variant="ghost"
                onClick={() => onCategoryChange("artists")}
                className="text-[#D9AD39] hover:text-[#D9AD39]/80"
              >
                Show all
              </Button>
            )}
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {(activeCategory === "all"
              ? results.artists.slice(0, 6)
              : results.artists
            ).map((artist) => (
              <Link
                key={artist._id}
                to={`/artist/${artist._id}`}
                className="group p-4 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="text-center">
                  <div className="w-20 h-20 mx-auto mb-3 rounded-full overflow-hidden bg-gray-200">
                    <img
                      src={artist.imageUrl}
                      alt={artist.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <p className="font-medium text-gray-900 group-hover:text-[#D9AD39] group-hover:underline">
                    {artist.name}
                  </p>
                  <p className="text-sm text-gray-500">Artist</p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Albums Results */}
      {results.albums.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Disc3 className="w-6 h-6" />
              Albums
            </h2>
            {activeCategory === "all" && results.albums.length > 6 && (
              <Button
                variant="ghost"
                onClick={() => onCategoryChange("albums")}
                className="text-[#D9AD39] hover:text-[#D9AD39]/80"
              >
                Show all
              </Button>
            )}
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {(activeCategory === "all"
              ? results.albums.slice(0, 6)
              : results.albums
            ).map((album) => (
              <div
                key={album._id}
                // to={`/albums/${album._id}`}
                className="group p-4 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="text-center">
                  <div className="w-20 h-20 mx-auto mb-3 rounded-lg overflow-hidden bg-gray-200">
                    <img
                      src={album.imageUrl}
                      alt={album.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <p className="font-medium text-gray-900 truncate hover:text-[#D9AD39] hover:underline">
                    <Link to={`/album/${album._id}`}>{album.title}</Link>
                  </p>
                  <p className="text-sm text-muted-foreground hover:text-[#D9AD39] hover:underline">
                    <Link to={`/artist/${album.artistId}`}>{album.artist}</Link>
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Playlists Results */}
      {results.playlists.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <ListMusic className="w-6 h-6" />
              Playlists
            </h2>
            {activeCategory === "all" && results.playlists.length > 6 && (
              <Button
                variant="ghost"
                onClick={() => onCategoryChange("playlists")}
                className="text-[#D9AD39] hover:text-[#D9AD39]/80"
              >
                Show all
              </Button>
            )}
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {(activeCategory === "all"
              ? results.playlists.slice(0, 6)
              : results.playlists
            ).map((playlist) => (
              <Link
                key={playlist._id}
                to={`/playlist/${playlist._id}`}
                className="group p-4 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="text-center">
                  <div className="w-20 h-20 mx-auto mb-3 rounded-lg overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                    {playlist.imageUrl ||
                    (playlist.songs.length > 0 &&
                      playlist.songs[0].imageUrl) ? (
                      <img
                        src={playlist.imageUrl || playlist.songs[0].imageUrl}
                        alt={playlist.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <ListMusic className="w-8 h-8 text-white" />
                    )}
                  </div>
                  <p className="font-medium text-gray-900 truncate group-hover:text-[#D9AD39]">
                    {playlist.name}
                  </p>
                  <p className="text-sm text-gray-500">
                    {playlist.songs.length} song
                    {playlist.songs.length !== 1 ? "s" : ""}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchResults;
