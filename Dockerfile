# Use a lightweight Node.js image
FROM node:20-slim

# Install pnpm
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy package files and install dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install

# Copy source code
COPY . .

# Build TypeScript
RUN pnpm run build

# Remove dev dependencies after build
RUN pnpm install --prod --ignore-scripts

# Expose port (Cloud Run uses $PORT env variable)
ENV PORT=8080
EXPOSE 8080

# Start the app
CMD ["node", "dist/index.js"]
