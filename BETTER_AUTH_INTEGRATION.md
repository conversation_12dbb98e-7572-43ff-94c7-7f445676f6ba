# Better Auth Integration Guide

This guide explains how to integrate Better Auth in your React (web) and Expo (React Native) apps.

---

## 1. Install Better Auth

**For React (Web):**
```sh
npm install better-auth
```

**For Expo (React Native):**
```sh
npm install better-auth better-auth/react-native
```

---

## 2. Configure Environment Variables

Add these to your `.env` file:

```
BETTER_AUTH_SECRET=bGH1ItT5VPyLEbzcZGM5VqV7miGaPBDZ
BETTER_AUTH_URL=http://localhost:5000
GOOGLE_CLIENT_ID=************-m4ralr16ol3j78nn8p3dk76abgou72cb.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
VITE_BACKEND_BASE_URL="http://localhost:5000"
VITE_FRONTEND_BASE_URL="http://localhost:3000"
```

---

## 3. Create Auth Client

**React (Web) Example:**
```typescript
import { createAuthClient } from "better-auth/react";

export const authClient = createAuthClient({
  baseURL: `${import.meta.env.VITE_BACKEND_BASE_URL}/api/auth`,
});

export const { useSession } = authClient;
export const signInWithGoogle = (options?: any) =>
  authClient.signIn.social({ provider: "google", ...options });
export const signOut = () => authClient.signOut();
```

**Expo (React Native) Example:**
```typescript
import { createAuthClient } from "better-auth/react-native";

export const authClient = createAuthClient({
  baseURL: "http://localhost:5000/api/auth", // Use your backend URL
});
```

---

## 4. Sign In with Google

**React (Web):**
```tsx
<Button
  onClick={async () => {
    await authClient.signIn.social({ provider: "google" });
  }}
>
  Sign in with Google
</Button>
```

**Expo (React Native):**
```tsx
<Button
  onPress={async () => {
    await authClient.signIn.social({ provider: "google" });
  }}
>
  Sign in with Google
</Button>
```

---

## 5. Handle Auth Callback

- On web, Better Auth handles the callback automatically.
- On mobile, ensure your redirect URI is set up correctly in Google Console and your backend.

---

## 6. Access Session

```typescript
const { data: session, isPending } = useSession();
```

---

## 7. Protect API Requests

Set the token in your API client (e.g., Axios):

```typescript
axiosInstance.defaults.headers.common["Authorization"] = `Bearer ${session?.session?.token}`;
```

---

## 8. Sign Out

```typescript
await authClient.signOut();
```

---

## Notes for Expo Integration

- Use `better-auth/react-native` for Expo.
- Configure deep linking for OAuth callbacks.
- Ensure your backend supports mobile redirect URIs.

---

**References:**
- [Better Auth Docs](https://better-auth.com/docs)
- [Expo Auth Guide](https://docs.expo.dev/guides/authentication/)
