{"design_system": {"name": "Music Player Right Sidebar", "description": "A dark-themed music player sidebar for displaying current song and artist information", "theme": {"primary_background": "#121212", "secondary_background": "#1e1e1e", "tertiary_background": "#2a2a2a", "text_primary": "#ffffff", "text_secondary": "#b3b3b3", "text_muted": "#6a6a6a", "accent_color": "#1db954", "border_color": "#333333", "button_background": "#404040", "button_hover": "#535353"}, "typography": {"heading_large": {"font_size": "24px", "font_weight": "bold", "color": "text_primary", "line_height": "1.2"}, "heading_medium": {"font_size": "18px", "font_weight": "600", "color": "text_primary", "line_height": "1.3"}, "heading_small": {"font_size": "14px", "font_weight": "600", "color": "text_primary", "line_height": "1.4"}, "body_text": {"font_size": "14px", "font_weight": "400", "color": "text_secondary", "line_height": "1.4"}, "caption": {"font_size": "12px", "font_weight": "400", "color": "text_muted", "line_height": "1.3"}}, "spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "xxl": "48px"}, "border_radius": {"small": "4px", "medium": "8px", "large": "12px", "pill": "24px"}}, "layout_structure": {"sidebar_width": "320px", "sections": [{"section_name": "hero_section", "description": "Large album artwork or artist image with overlay information", "height": "240px", "padding": "md", "background_style": "gradient_overlay", "content_positioning": "bottom_left"}, {"section_name": "artist_info", "description": "Artist name, follower count, and follow button", "padding": "md", "layout": "vertical_stack", "spacing_between_items": "sm"}, {"section_name": "credits_section", "description": "Song credits with expandable/collapsible content", "padding": "md", "layout": "vertical_list", "spacing_between_items": "sm"}, {"section_name": "queue_section", "description": "Next in queue with small album artwork and track info", "padding": "md", "layout": "horizontal_card", "spacing_between_items": "sm"}]}, "component_patterns": {"hero_image_card": {"structure": {"container": "relative", "background_image": "cover", "overlay": "linear_gradient_bottom", "content": "absolute_positioned"}, "styling": {"border_radius": "medium", "aspect_ratio": "16:9_or_square", "overflow": "hidden"}}, "artist_profile": {"structure": {"artist_name": "heading_medium", "stats": "caption", "action_button": "pill_button"}, "layout": "vertical_stack", "alignment": "left"}, "credits_list": {"structure": {"section_header": "heading_small", "credit_item": {"role": "caption", "name": "body_text"}}, "layout": "vertical_list", "dividers": "subtle_lines"}, "queue_item": {"structure": {"thumbnail": "square_image", "track_info": {"title": "body_text", "artist": "caption"}}, "layout": "horizontal_flex", "spacing": "sm", "alignment": "center"}, "pill_button": {"styling": {"background": "button_background", "border": "1px solid border_color", "border_radius": "pill", "padding": "sm md", "font_size": "12px", "font_weight": "600"}, "states": {"hover": "button_hover", "active": "accent_color"}}, "section_header": {"structure": {"title": "heading_small", "action_link": "caption"}, "layout": "horizontal_flex", "alignment": "space_between"}}, "interaction_patterns": {"expandable_sections": {"default_state": "collapsed", "trigger": "click_header_or_show_all", "animation": "slide_down", "max_visible_items": 3}, "follow_button": {"states": ["follow", "following", "unfollow"], "hover_behavior": "background_color_change", "click_behavior": "toggle_state"}, "queue_navigation": {"open_queue": "slide_out_panel", "queue_actions": "reorder_drag_drop"}}, "responsive_behavior": {"mobile": {"sidebar_width": "100vw", "layout": "full_screen_overlay", "hero_height": "200px"}, "tablet": {"sidebar_width": "280px", "hero_height": "220px"}, "desktop": {"sidebar_width": "320px", "hero_height": "240px"}}, "accessibility": {"color_contrast": "WCAG_AA_compliant", "focus_indicators": "visible_outline", "keyboard_navigation": "tab_order_logical", "screen_reader": "semantic_html_aria_labels"}, "implementation_guidelines": {"image_loading": "lazy_loading_with_placeholder", "data_structure": "real_time_updates", "performance": "virtualized_lists_for_long_queues", "animations": "reduced_motion_support"}}