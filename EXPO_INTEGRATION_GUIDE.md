# Expo/React Native Integration Guide

## Quick Start

### 1. Setup Expo Project
```bash
npx create-expo-app music-app
cd music-app
npx expo install expo-auth-session expo-crypto expo-web-browser
npm install axios socket.io-client @react-native-async-storage/async-storage
npm install expo-av expo-file-system expo-media-library
```

### 2. App Configuration (app.json)
```json
{
  "expo": {
    "name": "Music App",
    "slug": "music-app",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "light",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#ffffff"
    },
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.yourcompany.musicapp"
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#FFFFFF"
      },
      "package": "com.yourcompany.musicapp"
    },
    "web": {
      "favicon": "./assets/favicon.png"
    },
    "scheme": "musicapp",
    "plugins": [
      "expo-auth-session"
    ]
  }
}
```

### 3. Environment Configuration
Create `.env` file:
```env
EXPO_PUBLIC_API_BASE_URL=http://localhost:5000
EXPO_PUBLIC_SOCKET_URL=http://localhost:5000
EXPO_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
```

### 4. API Service Setup
```javascript
// src/services/api.js
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL || 'http://localhost:5000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add session token
api.interceptors.request.use(
  async (config) => {
    const sessionToken = await AsyncStorage.getItem('sessionToken');
    if (sessionToken) {
      config.headers.Authorization = `Bearer ${sessionToken}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      await AsyncStorage.removeItem('sessionToken');
      await AsyncStorage.removeItem('user');
      // Navigate to login screen
    }
    return Promise.reject(error);
  }
);

export default api;
```

### 5. Authentication Service
```javascript
// src/services/authService.js
import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import AsyncStorage from '@react-native-async-storage/async-storage';
import api from './api';

WebBrowser.maybeCompleteAuthSession();

const redirectUri = AuthSession.makeRedirectUri({
  scheme: 'musicapp',
  path: 'auth'
});

export const authService = {
  async signInWithGoogle() {
    try {
      const authUrl = `${process.env.EXPO_PUBLIC_API_BASE_URL}/api/auth/sign-in/google?redirect_uri=${encodeURIComponent(redirectUri)}`;
      
      const result = await WebBrowser.openAuthSessionAsync(
        authUrl,
        redirectUri
      );

      if (result.type === 'success') {
        // Extract session info from URL or make a session check
        await this.checkSession();
        return { success: true };
      }
      
      return { success: false, error: 'Authentication cancelled' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },

  async checkSession() {
    try {
      const response = await api.get('/api/auth/session');
      const { user, session } = response.data;
      
      if (session?.token) {
        await AsyncStorage.setItem('sessionToken', session.token);
      }
      await AsyncStorage.setItem('user', JSON.stringify(user));
      
      return { user, session };
    } catch (error) {
      throw error;
    }
  },

  async signOut() {
    try {
      await api.post('/api/auth/sign-out');
      await AsyncStorage.multiRemove(['sessionToken', 'user']);
      return { success: true };
    } catch (error) {
      // Clear local storage even if API call fails
      await AsyncStorage.multiRemove(['sessionToken', 'user']);
      return { success: false, error: error.message };
    }
  },

  async getStoredUser() {
    try {
      const userString = await AsyncStorage.getItem('user');
      return userString ? JSON.parse(userString) : null;
    } catch (error) {
      return null;
    }
  }
};
```

### 6. Music Service
```javascript
// src/services/musicService.js
import api from './api';

export const musicService = {
  // Songs
  getFeaturedSongs: () => api.get('/api/songs/featured'),
  getMadeForYouSongs: () => api.get('/api/songs/made-for-you'),
  getTrendingSongs: () => api.get('/api/songs/trending'),
  getAllSongs: () => api.get('/api/songs'),

  // Albums
  getAllAlbums: () => api.get('/api/albums'),
  getAlbumById: (id) => api.get(`/api/albums/${id}`),

  // Artists
  getAllArtists: () => api.get('/api/artists'),
  getArtistById: (id) => api.get(`/api/artists/${id}`),

  // Users
  getAllUsers: () => api.get('/api/users'),
  getMessages: (userId) => api.get(`/api/users/messages/${userId}`),

  // Admin functions
  createSong: (formData) => api.post('/api/admin/songs', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteSong: (id) => api.delete(`/api/admin/songs/${id}`),
  createAlbum: (formData) => api.post('/api/admin/albums', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  deleteAlbum: (id) => api.delete(`/api/admin/albums/${id}`),

  // Stats
  getStats: () => api.get('/api/stats'),
};
```

### 7. Socket Service
```javascript
// src/services/socketService.js
import io from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';

class SocketService {
  constructor() {
    this.socket = null;
  }

  async connect(userId) {
    const sessionToken = await AsyncStorage.getItem('sessionToken');
    
    this.socket = io(process.env.EXPO_PUBLIC_SOCKET_URL, {
      auth: {
        token: sessionToken
      },
      transports: ['websocket']
    });

    this.socket.on('connect', () => {
      console.log('Connected to server');
      this.socket.emit('joinRoom', userId);
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from server');
    });

    return this.socket;
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  sendMessage(receiverId, content) {
    if (this.socket) {
      this.socket.emit('sendMessage', { receiverId, content });
    }
  }

  onReceiveMessage(callback) {
    if (this.socket) {
      this.socket.on('receiveMessage', callback);
    }
  }

  offReceiveMessage() {
    if (this.socket) {
      this.socket.off('receiveMessage');
    }
  }
}

export const socketService = new SocketService();
```

### 8. Context & Hooks

#### Auth Context
```javascript
// src/contexts/AuthContext.js
import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService } from '../services/authService';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      // Check for stored user first
      const storedUser = await authService.getStoredUser();
      if (storedUser) {
        setUser(storedUser);
        // Verify session is still valid
        try {
          const { user: currentUser } = await authService.checkSession();
          setUser(currentUser);
          await checkAdminStatus();
        } catch (error) {
          // Session expired, clear stored user
          setUser(null);
        }
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkAdminStatus = async () => {
    try {
      await musicService.getStats(); // Admin-only endpoint
      setIsAdmin(true);
    } catch {
      setIsAdmin(false);
    }
  };

  const signIn = async () => {
    try {
      setLoading(true);
      const result = await authService.signInWithGoogle();
      
      if (result.success) {
        const { user: currentUser } = await authService.checkSession();
        setUser(currentUser);
        await checkAdminStatus();
        return { success: true };
      }
      
      return result;
    } catch (error) {
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      await authService.signOut();
      setUser(null);
      setIsAdmin(false);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      isAdmin,
      signIn,
      signOut,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
```

#### Music Player Hook
```javascript
// src/hooks/usePlayer.js
import { useState, useRef, useEffect } from 'react';
import { Audio } from 'expo-av';

export const usePlayer = () => {
  const [currentSong, setCurrentSong] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1.0);
  const [sound, setSound] = useState(null);

  useEffect(() => {
    return sound
      ? () => {
          sound.unloadAsync();
        }
      : undefined;
  }, [sound]);

  const playSong = async (song) => {
    try {
      // Unload previous sound
      if (sound) {
        await sound.unloadAsync();
      }

      // Load new sound
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: song.audioUrl },
        { shouldPlay: true, volume }
      );

      setSound(newSound);
      setCurrentSong(song);
      setIsPlaying(true);

      // Set up status updates
      newSound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded) {
          setCurrentTime(status.positionMillis / 1000);
          setDuration(status.durationMillis / 1000);
          setIsPlaying(status.isPlaying);
        }
      });
    } catch (error) {
      console.error('Error playing song:', error);
    }
  };

  const togglePlayPause = async () => {
    if (!sound) return;

    try {
      if (isPlaying) {
        await sound.pauseAsync();
      } else {
        await sound.playAsync();
      }
    } catch (error) {
      console.error('Error toggling playback:', error);
    }
  };

  const seekTo = async (time) => {
    if (!sound) return;

    try {
      await sound.setPositionAsync(time * 1000);
    } catch (error) {
      console.error('Error seeking:', error);
    }
  };

  const changeVolume = async (newVolume) => {
    if (!sound) return;

    try {
      await sound.setVolumeAsync(newVolume);
      setVolume(newVolume);
    } catch (error) {
      console.error('Error changing volume:', error);
    }
  };

  const stopPlayback = async () => {
    if (sound) {
      await sound.stopAsync();
      setIsPlaying(false);
      setCurrentTime(0);
    }
  };

  return {
    currentSong,
    isPlaying,
    currentTime,
    duration,
    volume,
    playSong,
    togglePlayPause,
    seekTo,
    changeVolume,
    stopPlayback,
  };
};
```

### 9. Component Examples

#### Login Screen
```javascript
// src/screens/LoginScreen.js
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useAuth } from '../contexts/AuthContext';

const LoginScreen = () => {
  const { signIn, loading } = useAuth();

  const handleSignIn = async () => {
    const result = await signIn();
    if (!result.success) {
      Alert.alert('Login Failed', result.error);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Welcome to Music App</Text>
      <Text style={styles.subtitle}>Discover and stream your favorite music</Text>
      
      <TouchableOpacity 
        style={styles.loginButton} 
        onPress={handleSignIn}
        disabled={loading}
      >
        <Text style={styles.loginButtonText}>
          {loading ? 'Signing in...' : 'Sign in with Google'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#1a1a1a',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#ccc',
    textAlign: 'center',
    marginBottom: 40,
  },
  loginButton: {
    backgroundColor: '#4285f4',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    minWidth: 200,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default LoginScreen;
```

#### Home Screen
```javascript
// src/screens/HomeScreen.js
import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  ScrollView, 
  StyleSheet, 
  RefreshControl,
  Alert 
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { musicService } from '../services/musicService';
import SongList from '../components/SongList';
import Header from '../components/Header';

const HomeScreen = () => {
  const { user } = useAuth();
  const [featuredSongs, setFeaturedSongs] = useState([]);
  const [madeForYou, setMadeForYou] = useState([]);
  const [trending, setTrending] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchMusicData();
  }, []);

  const fetchMusicData = async () => {
    try {
      const [featuredRes, madeForYouRes, trendingRes] = await Promise.all([
        musicService.getFeaturedSongs(),
        musicService.getMadeForYouSongs(),
        musicService.getTrendingSongs(),
      ]);

      setFeaturedSongs(featuredRes.data);
      setMadeForYou(madeForYouRes.data);
      setTrending(trendingRes.data);
    } catch (error) {
      Alert.alert('Error', 'Failed to load music data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchMusicData();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header user={user} />
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <SongList songs={featuredSongs} title="Featured Songs" />
        <SongList songs={madeForYou} title="Made For You" />
        <SongList songs={trending} title="Trending" />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
  },
  loadingText: {
    color: '#fff',
    fontSize: 18,
  },
});

export default HomeScreen;
```

#### Song List Component
```javascript
// src/components/SongList.js
import React from 'react';
import { 
  View, 
  Text, 
  FlatList, 
  TouchableOpacity, 
  Image, 
  StyleSheet 
} from 'react-native';
import { usePlayer } from '../hooks/usePlayer';

const SongList = ({ songs, title }) => {
  const { playSong } = usePlayer();

  const renderSong = ({ item }) => (
    <TouchableOpacity 
      style={styles.songCard} 
      onPress={() => playSong(item)}
    >
      <Image source={{ uri: item.imageUrl }} style={styles.songImage} />
      <View style={styles.songInfo}>
        <Text style={styles.songTitle} numberOfLines={1}>
          {item.title}
        </Text>
        <Text style={styles.songArtist} numberOfLines={1}>
          {item.artist}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <FlatList
        data={songs}
        renderItem={renderSong}
        keyExtractor={(item) => item._id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginHorizontal: 20,
    marginBottom: 15,
  },
  listContainer: {
    paddingHorizontal: 20,
  },
  songCard: {
    width: 150,
    marginRight: 15,
    backgroundColor: '#2a2a2a',
    borderRadius: 10,
    padding: 10,
  },
  songImage: {
    width: '100%',
    height: 130,
    borderRadius: 8,
    marginBottom: 10,
  },
  songInfo: {
    flex: 1,
  },
  songTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  songArtist: {
    color: '#ccc',
    fontSize: 12,
  },
});

export default SongList;
```

#### Music Player Component
```javascript
// src/components/MusicPlayer.js
import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  Image, 
  StyleSheet,
  Slider 
} from 'react-native';
import { usePlayer } from '../hooks/usePlayer';

const MusicPlayer = () => {
  const {
    currentSong,
    isPlaying,
    currentTime,
    duration,
    volume,
    togglePlayPause,
    seekTo,
    changeVolume,
  } = usePlayer();

  if (!currentSong) return null;

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.songInfo}>
        <Image source={{ uri: currentSong.imageUrl }} style={styles.songImage} />
        <View style={styles.textInfo}>
          <Text style={styles.songTitle} numberOfLines={1}>
            {currentSong.title}
          </Text>
          <Text style={styles.songArtist} numberOfLines={1}>
            {currentSong.artist}
          </Text>
        </View>
      </View>

      <View style={styles.controls}>
        <TouchableOpacity onPress={togglePlayPause} style={styles.playButton}>
          <Text style={styles.playButtonText}>
            {isPlaying ? '⏸️' : '▶️'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.progressContainer}>
        <Text style={styles.timeText}>{formatTime(currentTime)}</Text>
        <Slider
          style={styles.progressSlider}
          minimumValue={0}
          maximumValue={duration || 0}
          value={currentTime}
          onValueChange={seekTo}
          minimumTrackTintColor="#1DB954"
          maximumTrackTintColor="#666"
          thumbStyle={styles.sliderThumb}
        />
        <Text style={styles.timeText}>{formatTime(duration)}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#2a2a2a',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#444',
  },
  songInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  songImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
    marginRight: 15,
  },
  textInfo: {
    flex: 1,
  },
  songTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  songArtist: {
    color: '#ccc',
    fontSize: 14,
  },
  controls: {
    alignItems: 'center',
    marginBottom: 10,
  },
  playButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#1DB954',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButtonText: {
    fontSize: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeText: {
    color: '#ccc',
    fontSize: 12,
    minWidth: 40,
    textAlign: 'center',
  },
  progressSlider: {
    flex: 1,
    height: 20,
    marginHorizontal: 10,
  },
  sliderThumb: {
    backgroundColor: '#1DB954',
  },
});

export default MusicPlayer;
```

### 10. Main App Component
```javascript
// App.js
import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Audio } from 'expo-av';

import { AuthProvider, useAuth } from './src/contexts/AuthContext';
import LoginScreen from './src/screens/LoginScreen';
import HomeScreen from './src/screens/HomeScreen';
import MusicPlayer from './src/components/MusicPlayer';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return null; // Or a loading screen
  }

  return (
    <>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {user ? (
          <Stack.Screen name="Home" component={HomeScreen} />
        ) : (
          <Stack.Screen name="Login" component={LoginScreen} />
        )}
      </Stack.Navigator>
      <MusicPlayer />
    </>
  );
};

export default function App() {
  useEffect(() => {
    // Configure audio session
    Audio.setAudioModeAsync({
      allowsRecordingIOS: false,
      staysActiveInBackground: true,
      playsInSilentModeIOS: true,
      shouldDuckAndroid: true,
      playThroughEarpieceAndroid: false,
    });
  }, []);

  return (
    <AuthProvider>
      <NavigationContainer>
        <StatusBar style="light" />
        <AppNavigator />
      </NavigationContainer>
    </AuthProvider>
  );
}
```

### 11. Additional Dependencies
Add these to your `package.json`:
```json
{
  "dependencies": {
    "@react-navigation/native": "^6.1.7",
    "@react-navigation/stack": "^6.3.17",
    "react-native-screens": "~3.22.0",
    "react-native-safe-area-context": "4.6.3",
    "react-native-gesture-handler": "~2.12.0"
  }
}
```

### 12. Running the App
```bash
# Install dependencies
npm install

# Start the development server
npx expo start

# Run on iOS simulator
npx expo start --ios

# Run on Android emulator
npx expo start --android
```

This Expo integration guide provides a complete foundation for building a cross-platform music streaming mobile app that works with your backend API. The app includes authentication, music playback, real-time messaging capabilities, and a responsive UI optimized for mobile devices.