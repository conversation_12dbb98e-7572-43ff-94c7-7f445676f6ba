# Better Auth Setup Guide

## Environment Variables Required

Add these to your `.env` file:

```env
# Better Auth Configuration
BETTER_AUTH_SECRET=your-super-secret-key-here
BETTER_AUTH_URL=http://localhost:5000

# Google OAuth (replacing Clerk)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Database
MONGODB_URI=mongodb://localhost:27017/spotify-clone

# Other configurations
PORT=5000
NODE_ENV=development
```

## Getting Google OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to Credentials → Create Credentials → OAuth 2.0 Client ID
5. Set authorized redirect URIs to: `http://localhost:5000/api/auth/callback/google`
6. Copy the Client ID and Client Secret to your `.env` file

## Generate Better Auth Secret

Run this command to generate a secure secret:
```bash
openssl rand -base64 32
``` 