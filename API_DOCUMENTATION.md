# Music Streaming App - Complete API Documentation

## Overview
This is a comprehensive music streaming backend built with Express.js, MongoDB, and Better Auth. It provides a complete API for building music streaming applications with features like user authentication, music management, albums, artists, messaging, and admin functionality.

## Table of Contents
- [Authentication](#authentication)
- [Base URL & Environment](#base-url--environment)
- [Data Models](#data-models)
- [API Endpoints](#api-endpoints)
- [WebSocket Events](#websocket-events)
- [File Upload](#file-upload)
- [Error Handling](#error-handling)
- [Frontend Integration Examples](#frontend-integration-examples)

## Authentication

### Authentication System
The app uses **Better Auth** with Google OAuth integration, replacing the previous Clerk implementation.

### Authentication Flow
1. **Login**: Redirect users to `/api/auth/sign-in/google`
2. **Session Management**: Better Auth handles sessions via cookies
3. **Protected Routes**: Include session cookie in requests to protected endpoints
4. **Logout**: POST to `/api/auth/sign-out`

### Headers for Protected Routes
```javascript
// For authenticated requests, ensure cookies are included
fetch('/api/users', {
  credentials: 'include', // Important for session cookies
  headers: {
    'Content-Type': 'application/json'
  }
})
```

## Base URL & Environment

### Development
- Backend: `http://localhost:5000`
- Frontend: `http://localhost:3000`

### Environment Variables Required
```env
FRONTEND_BASE_URL=http://localhost:3000
BACKEND_BASE_URL=http://localhost:5000
BETTER_AUTH_SECRET=your-super-secret-key
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
MONGODB_URI=your-mongodb-connection-string
ADMIN_EMAIL=<EMAIL>
```

## Data Models

### User
```typescript
interface User {
  _id: string;
  fullName: string;
  imageUrl: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}
```

### Song
```typescript
interface Song {
  _id: string;
  title: string;
  artist: string;
  imageUrl: string;
  audioUrl: string;
  duration: number; // in seconds
  albumId?: string;
  artistId?: string;
  featuredArtists?: string[];
  bgColor?: string;
  createdAt: string;
  updatedAt: string;
}
```

### Album
```typescript
interface Album {
  _id: string;
  title: string;
  artist: string;
  imageUrl: string;
  releaseYear: number;
  genre: string;
  bgColor?: string;
  songs: string[]; // Array of song IDs
  artistId?: string;
  createdAt: string;
  updatedAt: string;
}
```

### Artist
```typescript
interface Artist {
  _id: string;
  name: string;
  imageUrl: string;
  bgColor?: string;
  createdAt: string;
  updatedAt: string;
}
```

### Message
```typescript
interface Message {
  _id: string;
  senderId: string; // User ID
  receiverId: string; // User ID
  content: string;
  createdAt: string;
  updatedAt: string;
}
```

## API Endpoints

### Authentication Endpoints
```
GET  /api/auth/sign-in/google     # Initiate Google OAuth
POST /api/auth/sign-out           # Sign out user
GET  /api/auth/session            # Get current session
```

### Songs
```
GET /api/songs                    # Get all songs (Admin only)
GET /api/songs/featured           # Get 6 random featured songs (Public)
GET /api/songs/made-for-you       # Get 4 random songs (Public)
GET /api/songs/trending           # Get 4 trending songs (Public)
```

**Example Response:**
```json
[
  {
    "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "title": "Song Title",
    "artist": "Artist Name",
    "imageUrl": "https://cloudinary.com/image.jpg",
    "audioUrl": "https://cloudinary.com/audio.mp3",
    "duration": 180
  }
]
```

### Albums
```
GET /api/albums                   # Get all albums (Public)
GET /api/albums/:albumId          # Get album by ID with populated songs (Public)
```

**Example Response:**
```json
{
  "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
  "title": "Album Title",
  "artist": "Artist Name",
  "imageUrl": "https://cloudinary.com/image.jpg",
  "releaseYear": 2024,
  "genre": "Pop",
  "songs": [
    {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d1",
      "title": "Song 1",
      "artist": "Artist Name",
      "audioUrl": "https://cloudinary.com/audio1.mp3"
    }
  ]
}
```

### Artists
```
GET /api/artists                  # Get all artists (Public)
GET /api/artists/:artistId        # Get artist by ID (Public)
```

### Users
```
GET /api/users                    # Get all users except current user (Auth required)
GET /api/users/messages/:userId   # Get messages between current user and specified user (Auth required)
```

### Admin Endpoints
All admin endpoints require authentication + admin privileges.

```
GET    /api/admin/check           # Check if current user is admin
POST   /api/admin/songs           # Create new song (multipart/form-data)
DELETE /api/admin/songs/:id       # Delete song by ID
POST   /api/admin/albums          # Create new album (multipart/form-data)
DELETE /api/admin/albums/:id      # Delete album by ID
POST   /api/admin/artists         # Create new artist (multipart/form-data)
DELETE /api/admin/artists/:id     # Delete artist by ID
```

**Create Song Request:**
```javascript
const formData = new FormData();
formData.append('title', 'Song Title');
formData.append('artist', 'Artist Name');
formData.append('albumId', 'album_id_here'); // optional
formData.append('duration', '180');
formData.append('audioFile', audioFile); // File object
formData.append('imageFile', imageFile); // File object

fetch('/api/admin/songs', {
  method: 'POST',
  credentials: 'include',
  body: formData
});
```

**Create Album Request:**
```javascript
const formData = new FormData();
formData.append('title', 'Album Title');
formData.append('artist', 'Artist Name');
formData.append('releaseYear', '2024');
formData.append('genre', 'Pop');
formData.append('imageFile', imageFile); // File object

fetch('/api/admin/albums', {
  method: 'POST',
  credentials: 'include',
  body: formData
});
```

### Playlists
```
GET /api/playlists/user           # Get user's playlists (Auth required)
GET /api/playlists/liked-songs    # Get user's liked songs as a playlist (Auth required)
GET /api/playlists/public         # Get public playlists with pagination (Public)
GET /api/playlists/:id            # Get playlist by ID (Auth required)
POST /api/playlists               # Create new playlist (Auth required)
PUT /api/playlists/:id            # Update playlist (Auth required)
DELETE /api/playlists/:id         # Delete playlist (Auth required)
POST /api/playlists/:id/songs     # Add song to playlist (Auth required)
DELETE /api/playlists/:id/songs/:songId  # Remove song from playlist (Auth required)
POST /api/playlists/:id/collaborators    # Add collaborator (Auth required)
DELETE /api/playlists/:id/collaborators/:collaboratorId  # Remove collaborator (Auth required)
```

**Playlist Model:**
```json
{
  "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
  "name": "My Playlist",
  "description": "My favorite songs",
  "imageUrl": "https://cloudinary.com/image.jpg",
  "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
  "songs": [
    {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
      "title": "Song Title",
      "artist": "Artist Name",
      "imageUrl": "https://cloudinary.com/image.jpg",
      "audioUrl": "https://cloudinary.com/audio.mp3",
      "duration": 180
    }
  ],
  "isPublic": true,
  "collaborators": [],
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

**Liked Songs Playlist Response:**
```json
{
  "_id": "liked-songs",
  "name": "Liked Songs",
  "description": "Songs you've liked",
  "imageUrl": null,
  "userId": "64f8a1b2c3d4e5f6a7b8c9d1",
  "songs": [
    {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
      "title": "Liked Song",
      "artist": "Artist Name",
      "imageUrl": "https://cloudinary.com/image.jpg",
      "audioUrl": "https://cloudinary.com/audio.mp3",
      "duration": 180,
      "likeCount": 150,
      "playCount": 1000
    }
  ],
  "isPublic": false,
  "collaborators": [],
  "isLikedSongs": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

**Create Playlist Request:**
```json
{
  "name": "My New Playlist",
  "description": "Optional description",
  "isPublic": true
}
```

## Data Integrity & User Synchronization

### User Model Synchronization
The application uses Better Auth for authentication but maintains a custom User model for playlist references. User synchronization is handled automatically through Better Auth callbacks:

- **onUserCreate**: Creates corresponding record in custom User collection
- **onSignIn**: Syncs user data between Better Auth and custom User model
- **Validation**: Playlist creation validates that referenced users exist

### Playlist Data Integrity
- **Schema Validation**: Playlist model enforces required userId field
- **Pre-save Hook**: Validates userId exists before saving playlist
- **Controller Validation**: Additional validation in createPlaylist endpoint
- **Error Handling**: Proper error messages for missing user references

### Database Maintenance
Use the provided script to identify and fix data integrity issues:

```bash
# Find problematic playlists
npm run fix-playlists find

# Delete problematic playlists (with 5-second warning)
npm run fix-playlists delete

# Assign orphaned playlists to a specific user
npm run fix-playlists assign <userId>
```

**Add Song to Playlist Request:**
```json
{
  "songId": "64f8a1b2c3d4e5f6a7b8c9d2"
}
```

### Engagement
```
POST /api/engagement/songs/:songId/like     # Like/Unlike song (Auth required)
POST /api/engagement/artists/:artistId/like # Like/Unlike artist (Auth required)
POST /api/engagement/artists/:artistId/follow # Follow/Unfollow artist (Auth required)
POST /api/engagement/plays/track            # Track song play (Auth required)
GET /api/engagement/user/liked-songs        # Get user's liked songs (Auth required)
GET /api/engagement/user/followed-artists   # Get user's followed artists (Auth required)
GET /api/engagement/user/play-history       # Get user's play history (Auth required)
POST /api/engagement/user/check             # Check user engagement status (Auth required)
GET /api/engagement/popular-artists         # Get popular artists (Public)
```

**Like/Unlike Response:**
```json
{
  "message": "Song liked successfully",
  "isLiked": true,
  "likeCount": 151
}
```

**Track Play Request:**
```json
{
  "songId": "64f8a1b2c3d4e5f6a7b8c9d2",
  "artistId": "64f8a1b2c3d4e5f6a7b8c9d3"
}
```

### Stats
```
GET /api/stats                    # Get platform statistics (Admin only)
```

**Response:**
```json
{
  "totalSongs": 150,
  "totalAlbums": 25,
  "totalUsers": 1000,
  "totalArtists": 50
}
```

## WebSocket Events

The app includes Socket.IO for real-time messaging.

### Connection
```javascript
import io from 'socket.io-client';

const socket = io('http://localhost:5000', {
  withCredentials: true
});
```

### Events
```javascript
// Send a message
socket.emit('sendMessage', {
  receiverId: 'user_id',
  content: 'Hello!'
});

// Listen for new messages
socket.on('receiveMessage', (message) => {
  console.log('New message:', message);
});

// Join user's room for receiving messages
socket.emit('joinRoom', userId);
```

## File Upload

### Supported File Types
- **Audio**: MP3, WAV, FLAC
- **Images**: JPG, PNG, WebP
- **Max Size**: 10MB per file

### Upload Process
1. Use `multipart/form-data` content type
2. Include both audio and image files for songs
3. Include image file for albums and artists
4. Files are temporarily stored and then uploaded to Cloudinary

## Error Handling

### Standard Error Responses
```json
{
  "message": "Error description",
  "error": "Detailed error info (development only)"
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized (not logged in)
- `403` - Forbidden (not admin)
- `404` - Not Found
- `500` - Internal Server Error

## Frontend Integration Examples

### React/Expo Authentication Hook
```javascript
import { useState, useEffect } from 'react';

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkSession();
  }, []);

  const checkSession = async () => {
    try {
      const response = await fetch('/api/auth/session', {
        credentials: 'include'
      });
      if (response.ok) {
        const session = await response.json();
        setUser(session.user);
      }
    } catch (error) {
      console.error('Session check failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = () => {
    window.location.href = '/api/auth/sign-in/google';
  };

  const logout = async () => {
    try {
      await fetch('/api/auth/sign-out', {
        method: 'POST',
        credentials: 'include'
      });
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return { user, loading, login, logout };
};
```

### API Service Class
```javascript
class MusicAPI {
  constructor(baseURL = 'http://localhost:5000') {
    this.baseURL = baseURL;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }

  // Songs
  async getFeaturedSongs() {
    return this.request('/api/songs/featured');
  }

  async getMadeForYouSongs() {
    return this.request('/api/songs/made-for-you');
  }

  async getTrendingSongs() {
    return this.request('/api/songs/trending');
  }

  // Albums
  async getAllAlbums() {
    return this.request('/api/albums');
  }

  async getAlbumById(albumId) {
    return this.request(`/api/albums/${albumId}`);
  }

  // Users
  async getAllUsers() {
    return this.request('/api/users');
  }

  async getMessages(userId) {
    return this.request(`/api/users/messages/${userId}`);
  }

  // Admin
  async createSong(formData) {
    return this.request('/api/admin/songs', {
      method: 'POST',
      headers: {}, // Remove Content-Type for FormData
      body: formData
    });
  }

  async createAlbum(formData) {
    return this.request('/api/admin/albums', {
      method: 'POST',
      headers: {}, // Remove Content-Type for FormData
      body: formData
    });
  }
}

export const musicAPI = new MusicAPI();
```

### React Native/Expo Considerations
```javascript
// For React Native, use AsyncStorage for session persistence
import AsyncStorage from '@react-native-async-storage/async-storage';

// Handle OAuth redirect in Expo
import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';

WebBrowser.maybeCompleteAuthSession();

const useAuthSession = () => {
  const [request, response, promptAsync] = AuthSession.useAuthRequest(
    {
      clientId: 'your-google-client-id',
      scopes: ['openid', 'profile', 'email'],
      redirectUri: AuthSession.makeRedirectUri({
        scheme: 'your-app-scheme'
      }),
    },
    AuthSession.discovery({
      authorizationEndpoint: 'http://localhost:5000/api/auth/sign-in/google',
    })
  );

  return { request, response, promptAsync };
};
```

## Development Tips

### CORS Configuration
The backend is configured to accept requests from `http://localhost:3000` by default. Update `FRONTEND_BASE_URL` in your environment variables for different origins.

### File Uploads
- Use FormData for file uploads
- Don't set Content-Type header manually for multipart requests
- Ensure files are within the 10MB limit

### Real-time Features
- Socket.IO is configured for real-time messaging
- Users must join their room to receive messages
- Messages are stored in MongoDB for persistence

### Admin Access
- Admin access is determined by email (contains 'admin' or matches `ADMIN_EMAIL` env var)
- Implement your own admin logic in the `requireAdmin` middleware if needed

This documentation provides everything needed to build React and Expo frontend applications that integrate with your music streaming backend.